name: Deploy to EC2 PROD

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18.19.1'

      - name: Install dependencies
        run: npm install

      - name: Build
        env:
          VITE_BACKEND_URL: ${{ vars.VITE_BACKEND_URL_PROD }}
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY_PROD }}
          #          change this according to the environment
          VITE_ENVIRONMENT: "prod"
        run: npm run build

      - name: Clean existing frontend files
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.AWS_HOST_PROD }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          port: 22
          script: |
            if [ -d "/home/<USER>/FRONTEND" ]; then
              sudo rm -rf /home/<USER>/FRONTEND/*
            fi

      - name: Copy files to EC2
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.AWS_HOST_PROD }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          port: 22
          source: "dist"
          target: "/home/<USER>/FRONTEND"
          overwrite: true

      - name: Set permissions on EC2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.AWS_HOST_PROD }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          port: 22
          script: |
            sudo chown -R ubuntu:ubuntu /home/<USER>/FRONTEND
            if [ -d "/home/<USER>/FRONTEND/dist" ]; then
              sudo chown -R www-data:www-data /home/<USER>/FRONTEND/dist
              sudo chmod -R 755 /home/<USER>/FRONTEND/dist
              sudo find /home/<USER>/FRONTEND/dist -type f -exec chmod 644 {} \;
            fi
