{"name": "clones-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@firebase/auth": "^1.10.0", "@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/react-stripe-js": "^3.5.1", "@tailwindcss/vite": "^4.0.7", "@tanstack/react-query": "^5.66.8", "@tanstack/react-query-devtools": "^5.66.8", "@tanstack/react-router": "^1.114.27", "@types/react-gtm-module": "^2.0.4", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^10.14.1", "jotai": "^2.12.1", "lucide-react": "^0.475.0", "motion": "^12.6.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-spinners": "^0.15.0", "react-webcam": "^7.2.0", "shadcn": "^2.4.0-canary.6", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.7", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.66.1", "@tanstack/react-router-devtools": "^1.114.27", "@tanstack/router-devtools": "^1.109.2", "@tanstack/router-plugin": "^1.114.27", "@types/node": "^22.13.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "tailwind-scrollbar": "^4.0.2", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}