import {StrictMode} from 'react'
import {createRoot} from 'react-dom/client'
import './index.css'
import {QueryClientProvider} from "@tanstack/react-query";
import {createRouter, RouterProvider} from "@tanstack/react-router";
import {AuthProvider} from "@/auth/auth-provider.tsx";
import {ThemeProvider} from "@/components/ThemeProvider.tsx";
import {ReactQueryDevtools} from "@tanstack/react-query-devtools";
import {queryClient} from "@/utils/query-client.ts";
import {routeTree} from "@/routeTree.gen.ts";
import { auth } from "@/auth/auth.ts";
import ReactGTM from 'react-gtm-module';
import {gtmId} from "@/utils/constants.ts";


const router = createRouter({
    routeTree,
    context: {
        queryClient: queryClient,
        user: auth.currentUser,
    },
});

declare module "@tanstack/react-router" {
    interface Register {
        router: typeof router;
    }
}

ReactGTM.initialize({ gtmId });


createRoot(document.getElementById('root')!).render(
    <StrictMode>
        <QueryClientProvider client={queryClient}>
            <ThemeProvider defaultTheme="light">
                <AuthProvider>
                    <RouterProvider router={router}/>
                </AuthProvider>
            </ThemeProvider>
            <ReactQueryDevtools buttonPosition="top-left"/>
        </QueryClientProvider>
    </StrictMode>,
)
