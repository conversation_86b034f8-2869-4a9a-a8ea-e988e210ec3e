import {auth} from "@/auth/auth.ts";
import {useSet<PERSON>tom} from "jotai";

import {PropsWithChildren, useEffect, useState} from "react";
import {UserAtom} from "@/auth/use-atom.ts";
import useApiAuth from "../utils/Api/use-api-auth";
import {useAuth} from "@/auth/use-auth.ts";
import EmailVerificationModal from "@/components/EmailVerificationModal";
import {useEmailVerificationModal} from "@/hooks/useEmailVerificationModal";

type AuthProviderProps = PropsWithChildren

export function AuthProvider({children}: AuthProviderProps) {
    const setUser = useSetAtom(UserAtom);
    const {loginAnonymously} = useAuth();
    const [load, setLoad] = useState(false);
    const {isOpen, setIsOpen} = useEmailVerificationModal();

    useApiAuth();
    useEffect(() => {
        return auth.onAuthStateChanged(async (user) => {
            if (user) {
                setUser(user);
                setLoad(true);
            } else {
                try {
                    const anonymousUser = await loginAnonymously();
                    setUser(anonymousUser);
                    setLoad(true);
                } catch (error) {
                    console.error("Anonymous sign-in failed:", error);
                    setUser(undefined);
                    setLoad(true);
                }
            }
        });
    }, [setUser]);


    if (!load) return null;
    return (
        <>
            {children}
            <EmailVerificationModal
                isOpen={isOpen}
                onOpenChange={setIsOpen}
            />
        </>
    );
}
