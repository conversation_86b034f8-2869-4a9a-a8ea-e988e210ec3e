import {useAtomValue} from "jotai";
import {
    createUserWithEmailAndPassword,
    EmailAuthProvider,
    linkWithCredential,
    linkWithPopup,
    reload,
    sendEmailVerification,
    sendPasswordResetEmail,
    signInAnonymously,
    signInWithEmailAndPassword,
    signInWithPopup,
    signOut,
    updateProfile,
} from "firebase/auth";
import {auth, googleAuthProvider} from "@/auth/auth.ts";
import {User<PERSON>tom} from "@/auth/use-atom.ts";
import {Api} from "@/utils/Api/Api.ts";
import {updateDbUserName} from "@/utils/hooks/user/useUser.ts";

export function useAuth() {
    const user = useAtomValue(UserAtom);

    async function loginWithEmailAndPassword(email: string, password: string) {
        return await signInWithEmailAndPassword(auth, email, password);
    }

    async function SignUpWithEmailAndPassword(email: string, password: string, name: string, linkAnonymous: boolean = false) {
        let userCredential;

        if (linkAnonymous && auth.currentUser?.isAnonymous) {
            const credential = EmailAuthProvider.credential(email, password);
            userCredential = await linkWithCredential(auth.currentUser, credential);
            await updateProfile(userCredential.user, {displayName: name});
        } else {
            userCredential = await createUserWithEmailAndPassword(auth, email, password);
            await updateProfile(userCredential.user, {displayName: name});
        }

        // Send email verification
        await sendEmailVerification(userCredential.user);

        // Get the user token for API authentication
        const token = await userCredential.user.getIdToken();

        try {
            // Fetch user data from backend
            const response = await Api.instance.get("auth/user/me", {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            const userData = response.data;

            // If the user's name is 'Anonymous', update it with the provided name
            if (userData && userData.name === 'Anonymous') {
                await updateDbUserName(name);
            }
        } catch (error) {
            console.error("Error fetching or updating user data:", error);
            // Continue with the sign-up process even if this fails
        }

        return userCredential;
    }

    async function loginWithGoogle(linkAnonymous: boolean = false) {
        try {
            let userCredential;

            if (linkAnonymous && auth.currentUser?.isAnonymous) {
                userCredential = await linkWithPopup(auth.currentUser, googleAuthProvider);
            } else {
                // Make sure we're using the correct provider and auth instance
                userCredential = await signInWithPopup(auth, googleAuthProvider);
            }

            // Get the user token for API authentication
            const token = await userCredential.user.getIdToken();

            try {
                // Fetch user data from backend
                const response = await Api.instance.get("auth/user/me", {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                });

                const userData = response.data;

                // If the user's name is 'Anonymous', update it with the Google display name
                if (userData && userData.name === 'Anonymous' && userCredential.user.displayName) {
                    await updateDbUserName(userCredential.user.displayName);
                }
            } catch (error) {
                console.error("Error fetching or updating user data:", error);
                // Continue with the sign-in process even if this fails
            }

            return userCredential;
        } catch (error) {
            console.error("Google authentication error:", error);
            throw error;
        }
    }


    async function loginAnonymously() {
        const userCredential = await signInAnonymously(auth);
        const user = userCredential.user;
        localStorage.setItem("userUid", user.uid);
        return user;
    }

    async function forgotPassword(email: string) {
        return await sendPasswordResetEmail(auth, email);
    }

    async function sendVerificationEmail() {
        if (auth.currentUser) {
            return await sendEmailVerification(auth.currentUser);
        }
        throw new Error("No user is currently signed in");
    }

    async function reloadUser() {
        if (auth.currentUser) {
            await reload(auth.currentUser);
            return auth.currentUser;
        }
        throw new Error("No user is currently signed in");
    }

    async function logout() {
        await signOut(auth);
        localStorage.removeItem("userUid");
        void window.location.reload();
    }

    return {
        user,
        loginWithEmailAndPassword,
        loginWithGoogle,
        SignUpWithEmailAndPassword,
        loginAnonymously,
        forgotPassword,
        sendVerificationEmail,
        reloadUser,
        logout,
    };
}
