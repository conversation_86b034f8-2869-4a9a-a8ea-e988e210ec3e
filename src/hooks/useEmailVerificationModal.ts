import {useState, useEffect} from 'react';
import {useAuth} from '@/auth/use-auth';

export function useEmailVerificationModal() {
    const {user} = useAuth();
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        // Show modal if user exists, is not anonymous, and email is not verified
        if (user && !user.isAnonymous && !user.emailVerified) {
            setIsOpen(true);
        } else {
            setIsOpen(false);
        }
    }, [user]);

    return {
        isOpen,
        setIsOpen,
    };
}
