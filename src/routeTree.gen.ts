/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as SharedImport } from './routes/_shared'
import { Route as AuthenticatedImport } from './routes/_authenticated'
import { Route as AuthImport } from './routes/_auth'
import { Route as SharedWaitingListImport } from './routes/_shared/waiting-list'
import { Route as SharedScriptImport } from './routes/_shared/script'
import { Route as SharedPaymentSuccessImport } from './routes/_shared/payment-success'
import { Route as SharedPaymentErrorImport } from './routes/_shared/payment-error'
import { Route as SharedContactUsImport } from './routes/_shared/contact-us'
import { Route as AuthSignupImport } from './routes/_auth/signup'
import { Route as AuthLoginImport } from './routes/_auth/login'
import { Route as SharedPricingIndexImport } from './routes/_shared/pricing/index'
import { Route as SharedPlaygroundIndexImport } from './routes/_shared/playground/index'
import { Route as SharedDashboardIndexImport } from './routes/_shared/_dashboard/index'
import { Route as AuthenticatedStudioIndexImport } from './routes/_authenticated/studio/index'

// Create/Update Routes

const SharedRoute = SharedImport.update({
  id: '/_shared',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedRoute = AuthenticatedImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const SharedWaitingListRoute = SharedWaitingListImport.update({
  id: '/waiting-list',
  path: '/waiting-list',
  getParentRoute: () => SharedRoute,
} as any)

const SharedScriptRoute = SharedScriptImport.update({
  id: '/script',
  path: '/script',
  getParentRoute: () => SharedRoute,
} as any)

const SharedPaymentSuccessRoute = SharedPaymentSuccessImport.update({
  id: '/payment-success',
  path: '/payment-success',
  getParentRoute: () => SharedRoute,
} as any)

const SharedPaymentErrorRoute = SharedPaymentErrorImport.update({
  id: '/payment-error',
  path: '/payment-error',
  getParentRoute: () => SharedRoute,
} as any)

const SharedContactUsRoute = SharedContactUsImport.update({
  id: '/contact-us',
  path: '/contact-us',
  getParentRoute: () => SharedRoute,
} as any)

const AuthSignupRoute = AuthSignupImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => AuthRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRoute,
} as any)

const SharedPricingIndexRoute = SharedPricingIndexImport.update({
  id: '/pricing/',
  path: '/pricing/',
  getParentRoute: () => SharedRoute,
} as any)

const SharedPlaygroundIndexRoute = SharedPlaygroundIndexImport.update({
  id: '/playground/',
  path: '/playground/',
  getParentRoute: () => SharedRoute,
} as any)

const SharedDashboardIndexRoute = SharedDashboardIndexImport.update({
  id: '/_dashboard/',
  path: '/',
  getParentRoute: () => SharedRoute,
} as any)

const AuthenticatedStudioIndexRoute = AuthenticatedStudioIndexImport.update({
  id: '/studio/',
  path: '/studio/',
  getParentRoute: () => AuthenticatedRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedImport
      parentRoute: typeof rootRoute
    }
    '/_shared': {
      id: '/_shared'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof SharedImport
      parentRoute: typeof rootRoute
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof AuthImport
    }
    '/_auth/signup': {
      id: '/_auth/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof AuthSignupImport
      parentRoute: typeof AuthImport
    }
    '/_shared/contact-us': {
      id: '/_shared/contact-us'
      path: '/contact-us'
      fullPath: '/contact-us'
      preLoaderRoute: typeof SharedContactUsImport
      parentRoute: typeof SharedImport
    }
    '/_shared/payment-error': {
      id: '/_shared/payment-error'
      path: '/payment-error'
      fullPath: '/payment-error'
      preLoaderRoute: typeof SharedPaymentErrorImport
      parentRoute: typeof SharedImport
    }
    '/_shared/payment-success': {
      id: '/_shared/payment-success'
      path: '/payment-success'
      fullPath: '/payment-success'
      preLoaderRoute: typeof SharedPaymentSuccessImport
      parentRoute: typeof SharedImport
    }
    '/_shared/script': {
      id: '/_shared/script'
      path: '/script'
      fullPath: '/script'
      preLoaderRoute: typeof SharedScriptImport
      parentRoute: typeof SharedImport
    }
    '/_shared/waiting-list': {
      id: '/_shared/waiting-list'
      path: '/waiting-list'
      fullPath: '/waiting-list'
      preLoaderRoute: typeof SharedWaitingListImport
      parentRoute: typeof SharedImport
    }
    '/_authenticated/studio/': {
      id: '/_authenticated/studio/'
      path: '/studio'
      fullPath: '/studio'
      preLoaderRoute: typeof AuthenticatedStudioIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_shared/_dashboard/': {
      id: '/_shared/_dashboard/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof SharedDashboardIndexImport
      parentRoute: typeof SharedImport
    }
    '/_shared/playground/': {
      id: '/_shared/playground/'
      path: '/playground'
      fullPath: '/playground'
      preLoaderRoute: typeof SharedPlaygroundIndexImport
      parentRoute: typeof SharedImport
    }
    '/_shared/pricing/': {
      id: '/_shared/pricing/'
      path: '/pricing'
      fullPath: '/pricing'
      preLoaderRoute: typeof SharedPricingIndexImport
      parentRoute: typeof SharedImport
    }
  }
}

// Create and export the route tree

interface AuthRouteChildren {
  AuthLoginRoute: typeof AuthLoginRoute
  AuthSignupRoute: typeof AuthSignupRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthLoginRoute: AuthLoginRoute,
  AuthSignupRoute: AuthSignupRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

interface AuthenticatedRouteChildren {
  AuthenticatedStudioIndexRoute: typeof AuthenticatedStudioIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedStudioIndexRoute: AuthenticatedStudioIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

interface SharedRouteChildren {
  SharedContactUsRoute: typeof SharedContactUsRoute
  SharedPaymentErrorRoute: typeof SharedPaymentErrorRoute
  SharedPaymentSuccessRoute: typeof SharedPaymentSuccessRoute
  SharedScriptRoute: typeof SharedScriptRoute
  SharedWaitingListRoute: typeof SharedWaitingListRoute
  SharedDashboardIndexRoute: typeof SharedDashboardIndexRoute
  SharedPlaygroundIndexRoute: typeof SharedPlaygroundIndexRoute
  SharedPricingIndexRoute: typeof SharedPricingIndexRoute
}

const SharedRouteChildren: SharedRouteChildren = {
  SharedContactUsRoute: SharedContactUsRoute,
  SharedPaymentErrorRoute: SharedPaymentErrorRoute,
  SharedPaymentSuccessRoute: SharedPaymentSuccessRoute,
  SharedScriptRoute: SharedScriptRoute,
  SharedWaitingListRoute: SharedWaitingListRoute,
  SharedDashboardIndexRoute: SharedDashboardIndexRoute,
  SharedPlaygroundIndexRoute: SharedPlaygroundIndexRoute,
  SharedPricingIndexRoute: SharedPricingIndexRoute,
}

const SharedRouteWithChildren =
  SharedRoute._addFileChildren(SharedRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof SharedRouteWithChildren
  '/login': typeof AuthLoginRoute
  '/signup': typeof AuthSignupRoute
  '/contact-us': typeof SharedContactUsRoute
  '/payment-error': typeof SharedPaymentErrorRoute
  '/payment-success': typeof SharedPaymentSuccessRoute
  '/script': typeof SharedScriptRoute
  '/waiting-list': typeof SharedWaitingListRoute
  '/studio': typeof AuthenticatedStudioIndexRoute
  '/': typeof SharedDashboardIndexRoute
  '/playground': typeof SharedPlaygroundIndexRoute
  '/pricing': typeof SharedPricingIndexRoute
}

export interface FileRoutesByTo {
  '': typeof AuthenticatedRouteWithChildren
  '/login': typeof AuthLoginRoute
  '/signup': typeof AuthSignupRoute
  '/contact-us': typeof SharedContactUsRoute
  '/payment-error': typeof SharedPaymentErrorRoute
  '/payment-success': typeof SharedPaymentSuccessRoute
  '/script': typeof SharedScriptRoute
  '/waiting-list': typeof SharedWaitingListRoute
  '/studio': typeof AuthenticatedStudioIndexRoute
  '/': typeof SharedDashboardIndexRoute
  '/playground': typeof SharedPlaygroundIndexRoute
  '/pricing': typeof SharedPricingIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_auth': typeof AuthRouteWithChildren
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/_shared': typeof SharedRouteWithChildren
  '/_auth/login': typeof AuthLoginRoute
  '/_auth/signup': typeof AuthSignupRoute
  '/_shared/contact-us': typeof SharedContactUsRoute
  '/_shared/payment-error': typeof SharedPaymentErrorRoute
  '/_shared/payment-success': typeof SharedPaymentSuccessRoute
  '/_shared/script': typeof SharedScriptRoute
  '/_shared/waiting-list': typeof SharedWaitingListRoute
  '/_authenticated/studio/': typeof AuthenticatedStudioIndexRoute
  '/_shared/_dashboard/': typeof SharedDashboardIndexRoute
  '/_shared/playground/': typeof SharedPlaygroundIndexRoute
  '/_shared/pricing/': typeof SharedPricingIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/login'
    | '/signup'
    | '/contact-us'
    | '/payment-error'
    | '/payment-success'
    | '/script'
    | '/waiting-list'
    | '/studio'
    | '/'
    | '/playground'
    | '/pricing'
  fileRoutesByTo: FileRoutesByTo
  to:
    | ''
    | '/login'
    | '/signup'
    | '/contact-us'
    | '/payment-error'
    | '/payment-success'
    | '/script'
    | '/waiting-list'
    | '/studio'
    | '/'
    | '/playground'
    | '/pricing'
  id:
    | '__root__'
    | '/_auth'
    | '/_authenticated'
    | '/_shared'
    | '/_auth/login'
    | '/_auth/signup'
    | '/_shared/contact-us'
    | '/_shared/payment-error'
    | '/_shared/payment-success'
    | '/_shared/script'
    | '/_shared/waiting-list'
    | '/_authenticated/studio/'
    | '/_shared/_dashboard/'
    | '/_shared/playground/'
    | '/_shared/pricing/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  SharedRoute: typeof SharedRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  SharedRoute: SharedRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_auth",
        "/_authenticated",
        "/_shared"
      ]
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/login",
        "/_auth/signup"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated.tsx",
      "children": [
        "/_authenticated/studio/"
      ]
    },
    "/_shared": {
      "filePath": "_shared.tsx",
      "children": [
        "/_shared/contact-us",
        "/_shared/payment-error",
        "/_shared/payment-success",
        "/_shared/script",
        "/_shared/waiting-list",
        "/_shared/_dashboard/",
        "/_shared/playground/",
        "/_shared/pricing/"
      ]
    },
    "/_auth/login": {
      "filePath": "_auth/login.tsx",
      "parent": "/_auth"
    },
    "/_auth/signup": {
      "filePath": "_auth/signup.tsx",
      "parent": "/_auth"
    },
    "/_shared/contact-us": {
      "filePath": "_shared/contact-us.tsx",
      "parent": "/_shared"
    },
    "/_shared/payment-error": {
      "filePath": "_shared/payment-error.tsx",
      "parent": "/_shared"
    },
    "/_shared/payment-success": {
      "filePath": "_shared/payment-success.tsx",
      "parent": "/_shared"
    },
    "/_shared/script": {
      "filePath": "_shared/script.tsx",
      "parent": "/_shared"
    },
    "/_shared/waiting-list": {
      "filePath": "_shared/waiting-list.tsx",
      "parent": "/_shared"
    },
    "/_authenticated/studio/": {
      "filePath": "_authenticated/studio/index.tsx",
      "parent": "/_authenticated"
    },
    "/_shared/_dashboard/": {
      "filePath": "_shared/_dashboard/index.tsx",
      "parent": "/_shared"
    },
    "/_shared/playground/": {
      "filePath": "_shared/playground/index.tsx",
      "parent": "/_shared"
    },
    "/_shared/pricing/": {
      "filePath": "_shared/pricing/index.tsx",
      "parent": "/_shared"
    }
  }
}
ROUTE_MANIFEST_END */
