import {createFileRoute, redirect} from "@tanstack/react-router";
import NavigationBar from "@/components/NavBar.tsx";

export const Route = createFileRoute("/_auth")({
    component: AuthenticatedComponent,
    beforeLoad: ({ context }) => {
        if (context.user && !context.user.isAnonymous) {
            throw redirect({ to: "/" });
        }
    },
});


function AuthenticatedComponent() {
    return (
        <>
            <NavigationBar />
        </>
    );
}
