import {
    createFileRoute,
    redirect,
} from "@tanstack/react-router";

// import NavigationBar from "@/components/NavBar.tsx";
import AuthenticatedNavBar from "@/components/AuthenticatedNavBar.tsx";

export const Route = createFileRoute("/_authenticated")({
    component: AuthenticatedComponent,
    beforeLoad: ({context}) => {
        if (!context.user) {
            throw redirect({to: "/login"});
        }
        if (context.user.isAnonymous) {
            throw redirect({to: "/login"});
        }

        if (context.user.emailVerified === false) {
            throw redirect({to: "/verification"});
        }
        console.log("Authenticated" , context.user);
    },
});

function AuthenticatedComponent() {
    return (
        <>
            <AuthenticatedNavBar/>
        </>
    );
}
