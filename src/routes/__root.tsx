import {useEffect, useRef} from "react";
import {createRootRoute, Outlet, useNavigate} from "@tanstack/react-router";
import {TanStackRouterDevtools} from "@tanstack/router-devtools";
import {auth} from "@/auth/auth.ts";
import {useAuth} from "@/auth/use-auth.ts";
import {User} from "firebase/auth";
import usePageTracking from "@/components/PageTracking.tsx";
import {useScrollReset} from "@/utils/hooks/useScrollReset";
import {ENVIRONMENT} from "@/utils/constants.ts";

export const Route = createRootRoute({
    component: RootLayout,
    beforeLoad: () => {
        return {user: auth.currentUser};
    },
});

function RootLayout() {
    const {user} = useAuth();
    const navigate = useNavigate();
    const previousUserRef = useRef<User | undefined>(user);
    usePageTracking();
    // Use the scroll reset hook to reset scroll position on navigation
    useScrollReset();

    useEffect(() => {
        const previousUser = previousUserRef.current;
        if (previousUser !== user) {
            void navigate({to: "/"});
        }
    }, [navigate, user]);

    return (
        <>
            <Outlet/>
            {
                ENVIRONMENT === "dev" && (
                    <TanStackRouterDevtools position="bottom-right" initialIsOpen={false}/>
                )
            }
        </>
    );
}
