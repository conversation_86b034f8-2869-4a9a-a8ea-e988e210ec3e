import {createFileRoute} from '@tanstack/react-router'
import {useEffect, useState} from 'react'
import {useForm} from 'react-hook-form'
import {zodResolver} from '@hookform/resolvers/zod'
import * as z from 'zod'

import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form'
import {Input} from '@/components/ui/input'
import {Button} from '@/components/ui/button'
import {Alert, AlertDescription} from '@/components/ui/alert'
import {cn} from '@/lib/utils'
import {X} from "lucide-react";
import {useSubmitWaitlist} from "@/utils/hooks/waitinglist/useSubmitWaitlist.ts";

export const Route = createFileRoute('/_shared/waiting-list')({
    component: WaitlistComponent,
})


const formSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Invalid email address'),
    product: z.enum(['Video Fusion', 'Video Multiplier', 'Video Cloner']),
    company: z.string().optional(),
})

interface ProductOption {
    id: string
    value: string
    title: string
    description: string
}

const productOptions: ProductOption[] = [
    {
        id: 'video-fusion',
        value: 'Video Fusion',
        title: 'Video Fusion',
        description: 'Create new videos by seamlessly blending ideas and scenes from your existing content.',
    },
    {
        id: "video-multiplier",
        value: "Video Multiplier",
        title: "Video Multiplier",
        description:
            "Generate multiple versions of your video with variations in length, focus, and theme.",
    },
    {
        id: "video-cloner",
        value: "Video Cloner",
        title: "Video Cloner",
        description:
            "Bring yourself to life in new videos using just an idea, script, or files—no camera needed.",
    },
];

export function WaitlistComponent() {
    const [showAlert, setShowAlert] = useState(false)
    const [alertMessage, setAlertMessage] = useState('')
    const [alertType, setAlertType] = useState<'success' | 'error'>('success')

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            email: '',
            company: '',
        },
    })

    const {mutate, isPending} = useSubmitWaitlist()

    useEffect(() => {
        const timer = setTimeout(() => {
            setShowAlert(false)
        }, 5000)

        return () => clearTimeout(timer)
    }, [alertMessage])

    async function onSubmit(values: z.infer<typeof formSchema>) {
        mutate(values, {
            onSuccess: (data) => {
                setAlertMessage(data.message)
                setAlertType('success')
                setShowAlert(true)
                form.reset()
            },
            onError: (error: any) => {
                if (error.response?.status === 400) {
                    setAlertMessage('Please fill in all required fields.')
                } else if (error.response?.status === 409) {
                    setAlertMessage('This email is already on our waitlist.')
                } else {
                    setAlertMessage('An unexpected error occurred. Please try again.')
                }
                setAlertType('error')
                setShowAlert(true)
            },
        })
    }

    return (
        <div className="min-h-screen bg-white mt-8">
            <div className="container mx-auto px-4">
                <h1 className="text-3xl md:text-4xl font-bold text-center text-[#F4804E] mb-4">
                    Join Our Waitlist
                </h1>
                <p className="text-lg text-gray-600 text-center max-w-3xl mx-auto mb-8">
                    Be the first to experience the future of AI-powered video creation.
                    Get early access to our innovative tools and shape the future of
                    content creation!
                </p>

                {showAlert && (
                    <Alert
                        variant={alertType === 'success' ? 'default' : 'destructive'}
                        className={`relative text-white ${alertType === 'success' ? 'bg-green-400' : 'bg-red-400'}`}
                    >
                        <button
                            onClick={() => setShowAlert(false)}
                            className="absolute top-2 right-2 text-white hover:text-gray-200"
                        >
                            <X size={20}/>
                        </button>
                        <AlertDescription className={"text-white"}>{alertMessage}</AlertDescription>
                    </Alert>

                )}

                <div className="bg-[rgba(103,58,183,0.1)] rounded-xl p-6 max-w-xl mx-auto">
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 ">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>Name *</FormLabel>
                                        <FormControl className="bg-white">
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="email"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>Email *</FormLabel>
                                        <FormControl className="bg-white">
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="product"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>Product of Highest Interest *</FormLabel>
                                        <div className="space-y-3">
                                            {productOptions.map((option) => (
                                                <FormItem key={option.id}>
                                                    <FormLabel
                                                        className={cn(
                                                            "border rounded-lg p-4 cursor-pointer transition-colors hover:bg-[rgba(103,58,183,0.05)]",
                                                            field.value === option.value
                                                                ? "border-[#673AB7] bg-[rgba(103,58,183,0.05)]"
                                                                : "border-[rgba(103,58,183,0.2)]"
                                                        )}
                                                    >
                                                        <div className="flex items-center justify-between">
                                                            <div className="flex flex-col gap-1">
                                                                <p
                                                                    className={`font-semibold ${field.value === option.value ? "text-[#673AB7]" : "text-gray-600"} text-lg`}
                                                                >
                                                                    {option.title}
                                                                </p>
                                                                <p className="text-sm text-gray-600">
                                                                    {option.description}
                                                                </p>
                                                            </div>
                                                            {field.value === option.value && (
                                                                <span className="text-[#673AB7]">✓</span>
                                                            )}
                                                        </div>
                                                        <FormControl>
                                                            <input
                                                                type="radio"
                                                                value={option.value}
                                                                checked={field.value === option.value}
                                                                onChange={() => field.onChange(option.value)}
                                                                className="hidden"
                                                            />
                                                        </FormControl>
                                                    </FormLabel>
                                                </FormItem>
                                            ))}
                                        </div>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="company"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel>Company (Optional)</FormLabel>
                                        <FormControl className="bg-white">
                                            <Input {...field} />
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />

                            <Button
                                type="submit"
                                className="w-full bg-[#673AB7] hover:bg-[#5c33a5] rounded-3xl py-6 hover:cursor-pointer"
                                disabled={isPending}
                            >
                                {isPending ? 'Submitting...' : 'Join Waitlist'}
                            </Button>
                        </form>
                    </Form>
                </div>
            </div>
        </div>
    );
}