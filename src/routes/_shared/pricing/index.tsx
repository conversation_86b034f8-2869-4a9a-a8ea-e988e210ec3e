import {Card, CardContent, CardHeader} from '@/components/ui/card';
import {createFileRoute, useNavigate, useLocation} from '@tanstack/react-router'
import {useState, useRef, useEffect, useMemo} from "react";
import {usePlans} from "@/utils/hooks/payments/usePlans.ts";
import {Tabs, TabsList, TabsTrigger} from "@/components/ui/tabs.tsx";
import {Button} from "@/components/ui/button.tsx";
import {PriceDisplay} from "@/components/PriceDisplay.tsx";
import {StripeCoupon, StripeProduct} from "@/utils/hooks/payments/interfaces.ts";
import {useCreateSubscription} from "@/utils/hooks/payments/useCreateSubscription.ts";
import {useCancelSubscription} from "@/utils/hooks/payments/useCancelSubscription.ts";
import {FeatureList} from "@/components/featureList.tsx";
import {UsageFees} from "@/components/UsageFees.tsx";
import {useAuth} from "@/auth/use-auth.ts";
import {useUserWithSubscription} from "@/utils/hooks/user/useUser.ts";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";
import {CheckCircle, InfoIcon, X} from "lucide-react";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog.tsx";
import {Alert, AlertDescription} from "@/components/ui/alert.tsx";

export const Route = createFileRoute('/_shared/pricing/')({
    component: PricingComponent,
})


function PricingComponent() {
    const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("yearly");
    const {data: products} = usePlans();
    const navigate = useNavigate();
    const {user} = useAuth();
    const location = useLocation();
    const addOnsSectionRef = useRef<HTMLDivElement>(null);
    const {data: userData} = useUserWithSubscription();

    // Check if user has a free plan
    const isFreePlan = useMemo(() => {
        const subscription = userData?.subscription;
        return !subscription || !subscription.status || subscription.status !== "active" || subscription.productName.includes("Free");
    }, [userData]);

    // Check if user has a creator subscription
    const isCreatorPlan = useMemo(() => {
        const subscription = userData?.subscription;
        return subscription && subscription.status === "active" && subscription.productName.includes("Creator");
    }, [userData]);

    // Check if subscription is already set to cancel at period end
    const isSubscriptionCanceling = useMemo(() => {
        const subscription = userData?.subscription;
        return subscription?.cancel_at_period_end === true;
    }, [userData]);

    // Check if we need to scroll to the AddOnsSection
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const section = searchParams.get('section');

        if (section === 'credits' && addOnsSectionRef.current) {
            setTimeout(() => {
                // Get the element's position
                if (addOnsSectionRef.current) {
                    const elementPosition = addOnsSectionRef.current.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - 20;
                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
            }, 300);
        }
    }, [location.search, products]);

    // Filter and prepare plans based on billing cycle
    const getFilteredPlans = () => {
        if (!products) return [];

        // Always include enterprise/studio plan
        const enterprisePlan = products.find(p => p.product_type === 'enterprise');

        // Filter creator plans based on billing cycle
        const creatorPlans = products.filter(p =>
            p.product_type === 'subscription' &&
            p.prices.some(price =>
                billingCycle === "monthly"
                    ? price.interval === "month"
                    : price.interval === "year"
            )
        );
        const freePlans = products.filter(p => p.product_type === 'free');

        const filteredFreePlans = freePlans.filter(p =>
            p.prices.some(price =>
                billingCycle === "monthly"
                    ? price.interval === "month"
                    : price.interval === "year"
            )
        );
        // Combine plans
        return [
            ...filteredFreePlans,
            ...creatorPlans,
            ...(enterprisePlan ? [enterprisePlan] : [])
        ];
    };

    const createSubscription = useCreateSubscription();
    const cancelSubscription = useCancelSubscription();
    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');

    // Auto-hide alert after 5 seconds
    useEffect(() => {
        if (showAlert) {
            const timer = setTimeout(() => {
                setShowAlert(false);
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [showAlert]);


    const handleSubscribe = async (product: StripeProduct) => {
        // Check if user is anonymous or not logged in
        if (!user || user.isAnonymous) {
            // Store the current URL for redirect after login
            localStorage.setItem('redirectAfterLogin', '/pricing');
            // Redirect to login page
            void navigate({to: "/login"});
            return;
        }

        try {
            if (product.product_type === 'one_time') {
                // Handle one-time purchase
                const price = product.prices[0];

                // Find the best available coupon if any
                const bestCoupon = price?.available_coupons?.length > 0
                    ? price.available_coupons.reduce((best, current) => {
                        if (!best) return current;

                        const currentDiscount = current.percent_off
                            ? price.amount * (current.percent_off / 100)
                            : current.amount_off || 0;

                        const bestDiscount = best.percent_off
                            ? price.amount * (best.percent_off / 100)
                            : best.amount_off || 0;

                        return currentDiscount > bestDiscount ? current : best;
                    }, null as StripeCoupon | null)
                    : null;

                const result = await createSubscription.mutateAsync({
                    priceId: price?.id,
                    isOneTime: true,
                    couponId: bestCoupon?.id
                });

                if (result?.url) {
                    window.location.href = result.url;
                }
            } else {
                // Handle subscription
                const price = product.prices.find(p =>
                    billingCycle === "monthly"
                        ? p.interval === "month"
                        : p.interval === "year"
                );

                if (!price?.id) {
                    // toast.error("No valid price found for this plan");
                    return;
                }

                // Find the best available coupon if any
                const bestCoupon = price?.available_coupons?.length > 0
                    ? price.available_coupons.reduce((best, current) => {
                        if (!best) return current;

                        const currentDiscount = current.percent_off
                            ? price.amount * (current.percent_off / 100)
                            : current.amount_off || 0;

                        const bestDiscount = best.percent_off
                            ? price.amount * (best.percent_off / 100)
                            : best.amount_off || 0;

                        return currentDiscount > bestDiscount ? current : best;
                    }, null as StripeCoupon | null)
                    : null;

                const result = await createSubscription.mutateAsync({
                    priceId: price.id,
                    isOneTime: false,
                    couponId: bestCoupon?.id
                });

                if (result?.url) {
                    window.location.href = result.url;
                }
            }
        } catch (error) {
            console.error('Subscription error:', error);
        }
    };


    const renderPlanButton = (product: StripeProduct) => {
        // Show Current Plan button with Cancel option if the product is marked as current plan
        if (product.is_current_plan) {
            // Only show cancel button for creator plans that aren't already canceling
            if (isCreatorPlan && product.name.includes('Creator')) {
                if (isSubscriptionCanceling) {
                    return (
                        <Button
                            className="w-full border-orange-500 text-orange-500 bg-transparent border-2 text-base shadow-md"
                            variant="outline"
                            disabled
                        >
                            Cancels at Period End
                        </Button>
                    );
                }
                return (
                    <Button
                        className="w-full border-red-500 text-red-500 bg-transparent border-2 text-base shadow-md hover:bg-red-500 hover:text-white hover:cursor-pointer"
                        variant="outline"
                        onClick={() => setShowCancelDialog(true)}
                        disabled={cancelSubscription.isPending}
                    >
                        {cancelSubscription.isPending ? "Processing..." : "Cancel Subscription"}
                    </Button>
                );
            }
            return (
                <Button
                    className="w-full text-[#673AB7]"
                    variant="outline"
                    disabled
                >
                    Current Plan
                </Button>
            );
        }

        // Only show upgrade/downgrade buttons for authenticated non-anonymous users
        const isAuthenticatedUser = user && !user.isAnonymous;

        switch (product.product_type) {
            case 'free':
                // If user has creator plan and is viewing free plan, show Downgrade button
                if (isAuthenticatedUser && isCreatorPlan) {
                    return (
                        <Button
                            className="w-full border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                            variant="outline"
                            onClick={() => handleSubscribe(product)}
                            disabled={createSubscription.isPending}
                        >
                            {createSubscription.isPending ? "Processing..." : "Downgrade"}
                        </Button>
                    );
                }

                // Default free plan button
                return (
                    <Button
                        className="w-full border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                        variant="outline"
                        onClick={() => handleSubscribe(product)}
                        disabled={createSubscription.isPending}
                    >
                        {createSubscription.isPending ? "Processing..." : "Start Free Trial"}
                    </Button>
                );

            case 'subscription':
                // If user has free plan and is viewing creator plan, show Upgrade button
                if (isAuthenticatedUser && isFreePlan && product.name.includes('Creator')) {
                    return (
                        <Button
                            className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                            onClick={() => handleSubscribe(product)}
                            disabled={createSubscription.isPending}
                        >
                            {createSubscription.isPending ? "Processing..." : "Upgrade"}
                        </Button>
                    );
                }

                // Default subscription button
                return (
                    <Button
                        className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                        onClick={() => handleSubscribe(product)}
                        disabled={createSubscription.isPending}
                    >
                        {createSubscription.isPending ? "Processing..." : "Subscribe"}
                    </Button>
                );

            case 'enterprise':
                return (
                    <Button
                        className="w-full border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                        variant="outline"
                        onClick={() => navigate({to: "/contact-us"})}
                    >
                        Contact Us
                    </Button>
                );

            case 'one_time':
                return (
                    <Button
                        className="w-full hover:cursor-pointer"
                        variant="outline"
                        onClick={() => handleSubscribe(product)}
                        disabled={createSubscription.isPending}
                    >
                        {createSubscription.isPending ? "Processing..." : "Purchase Credits"}
                    </Button>
                );

            default:
                return null;
        }
    };


    const filteredPlans = getFilteredPlans();
    const gridCols = filteredPlans.length <= 2 ? 'md:grid-cols-2' : 'md:grid-cols-3';

    const filterPlanName = (name: string) => {
        name = name.replace(` - ${billingCycle.charAt(0).toUpperCase() + billingCycle.slice(1)}`, '');
        return name.replace('Clones', '');
    };

    const filterDescription = (description: string) => {
        const patternsToRemove = [
            /Free Plan - /g,
            /Creator Plan - /g,
            /You get \$\d+ every month\./g
        ];

        // Apply each pattern to remove the matching parts
        let result = description;
        for (const pattern of patternsToRemove) {
            result = result.replace(pattern, '');
        }
        // Trim any extra whitespace
        return result.trim();

    };

    const handleCancelSubscription = async () => {
        try {
            const result = await cancelSubscription.mutateAsync();
            setShowCancelDialog(false);
            setAlertMessage(result.message || 'Your subscription has been canceled successfully.');
            setShowAlert(true);
        } catch (error) {
            console.error('Error canceling subscription:', error);
            setAlertMessage('Failed to cancel subscription. Please try again.');
            setShowAlert(true);
        }
    };

    return (
        <div className="min-h-screen w-full bg-[#FBFAFF]">
            <div className="max-w-7xl mx-auto px-4 py-12">
                {/* Success/Error Alert */}
                {showAlert && (
                    <div className="fixed top-4 right-4 z-50 max-w-md">
                        <Alert
                            className="bg-green-100 border-green-400 text-green-800 relative pr-10"
                        >
                            <CheckCircle className="h-5 w-5 text-green-600 absolute left-3 top-3"/>
                            <AlertDescription className="pl-7">{alertMessage}</AlertDescription>
                            <button
                                onClick={() => setShowAlert(false)}
                                className="absolute top-2 right-2 text-green-600 hover:text-green-800"
                            >
                                <X size={18}/>
                            </button>
                        </Alert>
                    </div>
                )}
                {/* Cancel Subscription Dialog */}
                <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Cancel Your Subscription</DialogTitle>
                            <DialogDescription>
                                Are you sure you want to cancel your subscription? You will still have access to your
                                current credits but your plan will immediately downgraded to the Free plan.
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <Button
                                variant="outline"
                                onClick={() => setShowCancelDialog(false)}
                                className="border-[#673AB7] text-[#673AB7]"
                            >
                                Keep Subscription
                            </Button>
                            <Button
                                variant="destructive"
                                onClick={handleCancelSubscription}
                                disabled={cancelSubscription.isPending}
                                className="bg-red-500 hover:bg-red-600"
                            >
                                {cancelSubscription.isPending ? "Processing..." : "Confirm Cancellation"}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
                <h1 className="text-[#f4804e] text-4xl font-bold text-center mb-7">Choose a plan that works for you</h1>
                {/* Billing Cycle Toggle */}
                <div className="flex flex-col items-center justify-center mb-8">
                    <Tabs
                        value={billingCycle}
                        onValueChange={(value) => setBillingCycle(value as "monthly" | "yearly")}
                        className="w-[300px]"
                    >
                        <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="monthly">Monthly</TabsTrigger>
                            <TabsTrigger value="yearly">Yearly</TabsTrigger>
                        </TabsList>
                    </Tabs>
                    <p className="mt-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-700 to-orange-400 font-semibold">
                        Save up to 33% on annual billing
                    </p>
                </div>

                {/* Plans Grid */}
                <div className={`grid grid-cols-1 ${gridCols} gap-6 mb-16 ${
                    filteredPlans.length === 2 ? 'max-w-4xl mx-auto' : ''
                }`}>
                    {filteredPlans.map((product) => (
                        <Card
                            key={`${product.id}-${billingCycle}`}
                            className={`border-solid ring-1 ring-gray-600 ${
                                product.is_current_plan
                                    ? 'border-[#673AB7] ring-2 ring-[#673AB7] ring-offset-2'
                                    : product.name.includes('Creator') && !product.name.includes('Studio')
                                        ? 'border-[#673AB7] relative shadow-md shadow-[#673AB7]'
                                        : 'border-muted hover:border-[#673AB7]'
                            }`}
                        >
                            <CardHeader>
                                <h3 className="text-2xl font-bold text-[#f4804e]">
                                    {filterPlanName(product.name)}
                                </h3>
                                <p className="text-muted-foreground">{filterDescription(product.description)}</p>
                                <PriceDisplay
                                    product={product}
                                    billingCycle={billingCycle}
                                />
                            </CardHeader>

                            <CardContent>
                                {renderPlanButton(product)}
                                <FeatureList
                                    features={product.features}
                                    billingCycle={billingCycle}
                                />
                                {/* Display usage fees for Free and Creator plans */}
                                {(product.product_type === 'free' ||
                                    (product.product_type === 'subscription' && product.name.toLowerCase().includes('creator'))) && (
                                    <UsageFees
                                        planType={product.product_type === 'free' ? 'free' : 'creator'}
                                    />
                                )}


                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Add-ons Section */}
                <div ref={addOnsSectionRef}>
                    <AddOnsSection
                        addOns={products?.filter(p => p.product_type === 'one_time') ?? []}
                        onPurchase={handleSubscribe}
                    />
                </div>
            </div>
        </div>
    );
}

function AddOnsSection({
                           addOns,
                           onPurchase
                       }: {
    addOns: StripeProduct[],
    onPurchase: (product: StripeProduct) => void
}) {
    if (addOns.length === 0) return null;

    const filterName = (name: string) => {
        return name.replace('Clones', '');
    };


    return (
        <section className="py-12">
            <div className="max-w-7xl mx-auto px-4">
                <div className="flex items-center justify-center mb-4">
                    <h2 className="text-3xl font-bold text-center text-[#f4804e]">Extra Credits</h2>
                </div>
                <p className="text-center text-muted-foreground mb-8 max-w-2xl mx-auto">
                    Need more processing power? Add extra credits to your plan.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {addOns.map((addon) => (
                        <Card key={addon.id} className="ring-1 ring-[#673AB7] shadow-sm shadow-[#673AB7] bg-background">
                            <CardHeader>
                                <h3 className="text-xl font-bold text-[#f4804e] text-center">{filterName(addon.name) + " "}
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <InfoIcon
                                                    className="h-5 w-5 text-[#673AB7] hover:cursor-pointer hover:text-[#F4804D]"/>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p className="max-w-xs">Any unused credits will be removed on your
                                                    current plan's renewal date.</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </h3>
                                <p className="text-muted-foreground text-sm text-center">
                                    {addon.description}
                                </p>
                                <div className="mt-4 text-center">
                                    <span className="text-3xl font-bold">
                                        ${addon.prices[0]?.amount ?? 0}
                                    </span>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <Button
                                    className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:text-white hover:cursor-pointer"
                                    variant="outline"
                                    onClick={() => onPurchase(addon)}
                                >
                                    Purchase Credits
                                </Button>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        </section>
    );
}