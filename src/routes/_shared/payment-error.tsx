import {createFileRoute, useNavigate} from '@tanstack/react-router'
import {XCircle} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";

export const Route = createFileRoute('/_shared/payment-error')({
    component: PaymentErrorComponent,
})

function PaymentErrorComponent() {
    const navigate = useNavigate();

    return (
        <div className="min-h-screen bg-background flex items-center justify-center">
            <div className="max-w-md w-full p-8 text-center">
                <div className="mb-6">
                    <XCircle className="h-16 w-16 text-red-500 mx-auto"/>
                </div>
                <h1 className="text-3xl font-bold mb-4">Payment Failed</h1>
                <p className="text-muted-foreground mb-8">
                    We couldn't process your payment. Please try again or contact support if the problem persists.
                </p>
                <div className="space-y-4">
                    <Button
                        className="w-full"
                        onClick={() => navigate({to: '/pricing'})}
                    >
                        Try Again
                    </Button>
                    <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => navigate({to: '/contact-us'})}
                    >
                        Contact Support
                    </Button>
                </div>
            </div>
        </div>
    );
}

