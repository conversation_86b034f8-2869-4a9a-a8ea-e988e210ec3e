import {createFileRoute, useNavigate} from '@tanstack/react-router'
import {CheckCircle} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";

export const Route = createFileRoute('/_shared/payment-success')({
    component: PaymentSuccessComponent,
})

function PaymentSuccessComponent() {
    const navigate = useNavigate();

    return (
        <div className="min-h-screen bg-background flex items-center justify-center">
            <div className="max-w-md w-full p-8 text-center">
                <div className="mb-6">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto"/>
                </div>
                <h1 className="text-3xl font-bold mb-4">Payment Successful!</h1>
                <p className="text-muted-foreground mb-8">
                    Thank you for your subscription. Your account has been successfully updated.
                </p>
                <div className="space-y-4">
                    <Button
                        className="w-full"
                        onClick={() => navigate({to: '/studio'})}
                    >
                        Go to Dashboard
                    </Button>
                </div>
            </div>
        </div>
    );
}