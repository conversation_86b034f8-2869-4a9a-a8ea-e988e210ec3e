import {createFileRoute} from '@tanstack/react-router'
import {useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {Button} from '@/components/ui/button';
import {Alert, AlertDescription} from '@/components/ui/alert';
import {useSubmitContactForm} from "@/utils/hooks/contactUs/useSubmitContactUs.ts";

export const Route = createFileRoute('/_shared/contact-us')({
    component: ContactForm,
})

interface FormData {
    name: string;
    email: string;
    subject: string;
    message: string;
}

function ContactForm() {
    const [showAlert, setShowAlert] = useState(false)
    const [alertMessage, setAlertMessage] = useState('')
    const [alertType, setAlertType] = useState<'success' | 'error'>('success')
    const form = useForm<FormData>();

    const mutation = useSubmitContactForm();

    useEffect(() => {
        const timer = setTimeout(() => {
            setShowAlert(false)
        }, 5000)

        return () => clearTimeout(timer)
    }, [alertMessage])

    const onSubmit = (data: FormData) => {
        mutation.mutate(data, {
            onSuccess: (data) => {
                setAlertMessage(data.message)
                setAlertType('success')
                setShowAlert(true)
            },
            onError: () => {
                setAlertMessage('You already submitted a message. Please try resending after some time.')
                setAlertType('error')
                setShowAlert(true)
            },
        });
    };


    return (
        <div className="min-h-screen bg-white mt-12">
            <main className="container mx-auto px-4 sm:px-6 lg:px-8">
                <h1 className="text-3xl sm:text-4xl font-bold text-center text-[#F4804E] mb-4">
                    Contact Us
                </h1>
                <p className="text-gray-600 text-center text-lg mb-8 max-w-2xl mx-auto">
                    Have questions, suggestions or feedback? We'd love to hear from you!
                </p>

                {showAlert && (
                    <>
                        <Alert
                            variant={alertType === 'success' ? 'default' : 'destructive'}
                            className={`relative pb-2 ${alertType === 'success' ? 'bg-[#d1e7dd] text-[#0f5132] mt-4 border border-[#badbcc]' : 'bg-red-100 text-red-700 p-4 mt-4 rounded-lg'}`}
                        >
                            <AlertDescription
                                className={` ${alertType === 'success' ? 'text-[#0f5132]' : 'text-red-700'}`}>{alertMessage}</AlertDescription>
                        </Alert>
                        <div className={"pb-10"}/>
                    </>
                )}
                <div className="bg-[#673AB71A] rounded-xl p-6  max-w-2xl mx-auto shadow-md border border-gray-400">
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <FormField
                                control={form.control}
                                name="name"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel className="text-gray-700">Name *</FormLabel>
                                        <FormControl>
                                            <Input {...field}
                                                   className={"mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}
                                                   required/>
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="email"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel className="text-gray-700">Email *</FormLabel>
                                        <FormControl>
                                            <Input {...field}
                                                   className={"mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}
                                                   type="email" required/>
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="subject"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel className="text-gray-700">Subject *</FormLabel>
                                        <FormControl>
                                            <Input
                                                className={"mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"} {...field}
                                                required/>
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="message"
                                render={({field}) => (
                                    <FormItem>
                                        <FormLabel className="text-gray-700">Message *</FormLabel>
                                        <FormControl>
                                            <Textarea
                                                className={"mt-1 bg-white border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}  {...field}
                                                rows={5} required/>
                                        </FormControl>
                                        <FormMessage/>
                                    </FormItem>
                                )}
                            />
                            <div className="flex justify-end mt-4">
                                <Button
                                    type="submit"
                                    className="py-5 px-8 w-40 rounded-[20px] text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                                    disabled={mutation.isPending}
                                >
                                    {mutation.isPending ? 'Submitting...' : 'Submit'}
                                </Button>
                            </div>
                        </form>
                    </Form>
                </div>
            </main>
        </div>
    );
}