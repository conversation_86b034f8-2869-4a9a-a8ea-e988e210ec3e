import {createFileRoute} from '@tanstack/react-router'
import {useRef} from "react";

export const Route = createFileRoute('/_shared/script')({
    component: ScriptComponent,
})

function ScriptComponent() {

    const instructions = [
        {
            heading: "0:00 - 0:10 | Enthusiastic:",
            script:
                "This is my enthusiastic style: I scream, you scream, we all scream for ice cream! I scream, you scream, we all scream for ice cream!",
        },
        {
            heading: "0:10 - 0:20 | Explanatory:",
            script:
                "This is my explanatory style: Twinkle, twinkle, little star, how I wonder what you are! up above the world so high, like a diamond in the sky.",
        },
        {
            heading: "0:20 - 0:30 | Smiling/Friendly:",
            script:
                "This is my smiling and friendly style: Life is like a box of chocolates. You never know what you’re gonna get",
        },
        {
            heading: "0:30 - 0:40 | Curious/Exploratory:",
            script:
                "This is my curious and exploratory style: How much wood would a woodchuck chuck if a woodchuck could chuck wood?",
        },
        {
            heading: "0:40 - 0:50 | Storytelling:",
            expressions:
                "(Hands moving slowly, almost as if painting a picture in the air, gaze slightly upward)",
            script:
                "Four score and seven years ago, our fathers brought forth on this continent a new nation, conceived in liberty, and dedicated to the proposition that all men are created equal.",
        },
        {
            heading: "0:50 - 1:00 | Serious/Professional:",
            script:
                "I pledge allegiance to the flag of the United States of America, and to the republic for which it stands, one nation under God, indivisible, with liberty and justice for all.",
        },
    ];
    const scriptContainerRef = useRef<HTMLDivElement>(null);

    return (
        <div className="flex flex-col justify-center items-center w-full pt-8 sm:pt-10">
            <h2 className="text-2xl sm:text-3xl font-bold mb-4 text-[#f4804e]">Magic Script</h2>
            <div className={"p-2 sm:p-4 rounded-md max-w-3xl w-full"}>
                <div
                    ref={scriptContainerRef}
                    className="w-full h-full overflow-y-auto bg-[#F7F5FB] p-2 sm:p-4 rounded-md space-y-3 sm:space-y-4 border border-[#F0EBF8] relative scrollbar-thin scrollbar-thumb-[#673AB7] scrollbar-track-transparent">
                    {instructions.map((item, index) => (
                        <div key={index} className="text-center">
                            <h3 className="text-base sm:text-lg font-semibold">{item.heading}</h3>
                            <p className="text-gray-700 text-sm sm:text-base">{item.script}</p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}
