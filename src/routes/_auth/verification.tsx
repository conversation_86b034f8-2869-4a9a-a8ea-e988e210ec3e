import {createFileRout<PERSON>, <PERSON>, useNavigate} from '@tanstack/react-router'
import {useEffect, useRef, useState} from "react";
import {Button} from "@/components/ui/button.tsx";

export const Route = createFileRoute('/_shared/verification')({
    component: VerificationComponent,
})

function VerificationComponent() {

    // const { user } = useAuth()
    const [message, setMessage] = useState<string | null>(null)
    const [error, setError] = useState<string | null>(null)
    const messageRef = useRef<HTMLDivElement>(null)
    const navigate = useNavigate();

    useEffect(() => {

    }, [navigate]);

    useEffect(() => {
        if (message && messageRef.current) {
            messageRef.current.scrollIntoView({behavior: 'smooth', block: 'center'})
        }
    }, [message])

    const handleResend = async () => {
        setMessage(null)
        setError(null)
        try {
            setMessage('Verification email resent. Please check your inbox.')
        } catch (err: any) {
            setError('Failed to resend verification email. Please try again later.')
            console.error('Resend Email Verification Error:', err)
        }
    }

    const handleLogout = async () => {
    }

    return (
        <div className="flex items-center justify-center min-h-screen bg-[#FBFAFF] px-4 sm:px-8">
            <div className="p-8 bg-white rounded-lg shadow-md w-96 mx-auto border border-gray-300">
                <h1 className="text-2xl font-bold mb-6 text-center text-[#f4804e]">
                    Verify Your Email
                </h1>

                {message && (
                    <div
                        ref={messageRef}
                        className="mb-4 text-green-600 text-sm text-center"
                    >
                        {message}
                    </div>
                )}

                {error && (
                    <div
                        ref={messageRef}
                        className="mb-4 text-red-600 text-sm text-center"
                    >
                        {error}
                    </div>
                )}

                <p className="text-gray-700 mb-6 text-center">
                    We've sent a verification link to your email address. Please check your
                    inbox and click the link to verify your account.
                </p>

                <Button
                    onClick={handleResend}
                    className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer mb-4"
                >
                    Resend Verification Email
                </Button>

                <Button
                    variant="outline"
                    onClick={handleLogout}
                    className="w-full border-gray-300 text-gray-700 hover:bg-gray-100"
                >
                    Log Out
                </Button>

                <p className="mt-4 text-center text-sm text-gray-600">
                    Already verified?{' '}
                    <Link
                        to="/login"
                        className="text-[#E46F3E] font-semibold hover:underline cursor-pointer"
                    >
                        Sign in
                    </Link>
                </p>
            </div>
        </div>
    )


}

