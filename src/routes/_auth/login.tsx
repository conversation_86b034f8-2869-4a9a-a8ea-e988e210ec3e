import {createFileRoute, <PERSON>, useNavigate} from '@tanstack/react-router'
import {Input} from "@/components/ui/input.tsx";
import {Button} from "@/components/ui/button.tsx";
import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import * as z from 'zod';
import {useAuth} from "@/auth/use-auth.ts";
import {useState} from "react";
import {GoogleIcon} from '@/components/ui/GoogleIcon';
import {Separator} from "@/components/ui/separator.tsx";
import {Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription} from "@/components/ui/dialog";

// Zod validation schema
const formSchema = z.object({
    email: z.string().email("Invalid email address"),
    password: z.string().min(8, "Password must be at least 8 characters")
});

export const Route = createFileRoute('/_auth/login')({
    component: LoginComponent,
})

function LoginComponent() {

    const navigate = useNavigate();

    const {loginWithEmailAndPassword, loginWithGoogle, forgotPassword} = useAuth();

    const [loginError, setLoginError] = useState<string | null>(null);
    const [forgotPasswordOpen, setForgotPasswordOpen] = useState(false);
    const [resetEmail, setResetEmail] = useState("");
    const [resetMessage, setResetMessage] = useState<string | null>(null);
    const [resetError, setResetError] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Setup form with Zod resolver
    const {
        register,
        handleSubmit,
        formState: {errors}
    } = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: "",
            password: ""
        }
    });

    const onSubmit = async (data: z.infer<typeof formSchema>) => {
        try {
            const userCredential = await loginWithEmailAndPassword(data.email, data.password);
            setLoginError(null);

            // Check if email is verified
            if (!userCredential.user.emailVerified) {
                navigate({to: '/verification'});
                return;
            }

            // Check if there's a redirect URL stored
            const redirectUrl = localStorage.getItem('redirectAfterLogin');
            if (redirectUrl) {
                localStorage.removeItem('redirectAfterLogin'); // Clear it after use
                navigate({to: redirectUrl});
            }
        } catch (error) {
            console.error("Error signing in:", error);
            setLoginError("Wrong Email or Password");
        }
    };

    const loginWithGoogleHandler = async () => {
        try {
            await loginWithGoogle();
            setLoginError(null);

            // Check if there's a redirect URL stored
            const redirectUrl = localStorage.getItem('redirectAfterLogin');
            if (redirectUrl) {
                localStorage.removeItem('redirectAfterLogin'); // Clear it after use
                navigate({to: redirectUrl});
            }
        } catch (error) {
            console.error("Error signing in with Google:", error);
            setLoginError("Error signing in with Google");
        }
    };

    // Handle Password Reset Submission
    const handleForgotPassword = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        setResetMessage(null);
        setResetError(null);

        try {
            await forgotPassword(resetEmail);
            setResetMessage("Check your email for a password reset link");
            setResetError(null);
        } catch (error) {
            console.error("Error resetting password:", error);
            setResetError("Failed to send reset email. Please try again.");
            setResetMessage(null);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="flex items-center justify-center min-h-screen bg-[#FBFAFF] px-4 sm:px-8">
            <div className="p-8 bg-white rounded-lg shadow-md w-96 border border-gray-300">
                <h1 className="text-2xl font-bold mb-6 text-center text-[#f4804e]">Sign In</h1>

                {/* Login Form */}
                <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                            Email
                        </label>
                        <Input
                            type="email"
                            id="email"
                            {...register("email")}
                            className="mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        {errors.email && (
                            <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                        )}
                    </div>
                    <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                            Password
                        </label>
                        <Input
                            type="password"
                            id="password"
                            {...register("password")}
                            className="mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        {errors.password && (
                            <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
                        )}
                    </div>
                    <Button
                        type="submit"
                        className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                    >
                        Sign In
                    </Button>
                </form>

                {/* Forgot Password Link */}
                <p className="text-right text-sm mt-2">
                    <Button
                        variant="link"
                        className="text-[#E46F3E] font-semibold hover:underline  cursor-pointer"
                        onClick={() => setForgotPasswordOpen(true)}
                    >
                        Forgot Password?
                    </Button>
                </p>

                {/* Login Error */}
                {loginError && <div className="text-red-500 text-sm mt-2">{loginError}</div>}

                {/* Google Sign-In */}
                <div className="mt-6">
                    <Separator className="my-4"/>
                    <Button
                        variant="outline"
                        className="w-full border-gray-300 text-gray-700 active:bg-gray-100 transition-colors hover:cursor-pointer"
                        onClick={loginWithGoogleHandler}
                    >
                        <GoogleIcon className="h-4 w-4"/>
                        Sign in with Google
                    </Button>
                </div>

                {/* Sign Up Link */}
                <p className="mt-4 text-center text-sm text-gray-600">
                    Don't have an account?{" "}
                    <Link
                        to="/signup"
                        className="text-[#E46F3E] font-semibold hover:underline  cursor-pointer"
                    >
                        Sign up
                    </Link>
                </p>
            </div>

            {/* Forgot Password Modal */}
            <Dialog open={forgotPasswordOpen} onOpenChange={setForgotPasswordOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle className="text-2xl font-bold text-center text-[#f4804e]">Forgot
                            Password</DialogTitle>
                        <DialogDescription className="text-center">
                            Enter your email to receive a reset link.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleForgotPassword} className="space-y-4 mt-4">
                        <Input
                            type="email"
                            value={resetEmail}
                            onChange={(e) => setResetEmail(e.target.value)}
                            placeholder="Enter your email"
                            required
                        />
                        <Button
                            type="submit"
                            className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? "Sending..." : "Send Reset Link"}
                        </Button>
                        {resetMessage && <div className="text-green-500 text-sm">{resetMessage}</div>}
                        {resetError && <div className="text-red-500 text-sm">{resetError}</div>}
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}
