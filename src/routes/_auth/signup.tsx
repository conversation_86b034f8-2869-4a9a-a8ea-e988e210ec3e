import {createFile<PERSON>out<PERSON>, <PERSON>} from '@tanstack/react-router'
import {Input} from "@/components/ui/input.tsx";
import {Button} from "@/components/ui/button.tsx";
import {z} from "zod";
import {zodResolver} from "@hookform/resolvers/zod";
import {Controller, useForm} from "react-hook-form";
import {useAuth} from "@/auth/use-auth.ts";
import {Separator} from "../../components/ui/separator";
import {GoogleIcon} from "@/components/ui/GoogleIcon";
import {Checkbox} from "@/components/ui/checkbox.tsx";
import {useState, useRef, useEffect} from 'react';
import {TermsAndConditionsDialog} from "@/components/TermsAndConditionsDialog";

export const Route = createFileRoute('/_auth/signup')({
    component: SignUpComponent,
})

const formSchema = z.object({
    email: z.string().email("Invalid email address"),
    name: z.string().min(3, "Name must be at least 3 characters"),
    password: z.string().min(8, "Password must be at least 8 characters"),
    linkProgress: z.boolean().optional(),
    acceptTerms: z.boolean().refine(val => val === true, {
        message: "You must accept the Terms and Conditions"
    }),
});

function SignUpComponent() {
    const {SignUpWithEmailAndPassword, loginWithGoogle} = useAuth();
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [termsDialogOpen, setTermsDialogOpen] = useState(false);
    // Track terms acceptance outside the form for Google sign-in
    const [termsAccepted, setTermsAccepted] = useState(false);

    // References to error message elements for auto-scrolling
    const errorMessageRef = useRef<HTMLDivElement>(null);
    const nameErrorRef = useRef<HTMLParagraphElement>(null);
    const emailErrorRef = useRef<HTMLParagraphElement>(null);
    const passwordErrorRef = useRef<HTMLParagraphElement>(null);
    const termsErrorRef = useRef<HTMLParagraphElement>(null);

    // Auto-scroll to error message when it appears
    useEffect(() => {
        if (errorMessage && errorMessageRef.current) {
            errorMessageRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }, [errorMessage]);

    const {
        register,
        handleSubmit,
        formState: {errors},
        control,
        watch,
    } = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
            password: '',
            name: '',
            linkProgress: false,
            acceptTerms: false,
        },
    });


    // Auto-scroll to the first form validation error
    useEffect(() => {
        if (errors.name && nameErrorRef.current) {
            nameErrorRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        } else if (errors.email && emailErrorRef.current) {
            emailErrorRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        } else if (errors.password && passwordErrorRef.current) {
            passwordErrorRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        } else if (errors.acceptTerms && termsErrorRef.current) {
            termsErrorRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }, [errors.name, errors.email, errors.password, errors.acceptTerms]);

    const linkProgressValue = watch('linkProgress');

    const onSubmit = async (data: z.infer<typeof formSchema>) => {
        setErrorMessage(null); // Reset error message before new attempt
        try {
            await SignUpWithEmailAndPassword(
                data.email,
                data.password,
                data.name,
                data.linkProgress ?? false
            );
            // Check if there's a redirect URL stored
            const redirectUrl = localStorage.getItem('redirectAfterLogin');
            if (redirectUrl) {
                localStorage.removeItem('redirectAfterLogin'); // Clear it after use
                window.location.href = redirectUrl; // Use window.location for full page navigation
            } else {
                window.location.reload(); // Reload the page to update the user state
            }
        } catch (error: any) {
            switch (error.code) {
                case 'auth/email-already-in-use':
                    setErrorMessage('The email address is already in use. Please try logging in.');
                    break;
                case 'auth/invalid-email':
                    setErrorMessage('The email address is invalid. Please enter a valid email.');
                    break;
                case 'auth/weak-password':
                    setErrorMessage('Password is too weak. Please use a stronger password.');
                    break;
                case 'auth/network-request-failed':
                    setErrorMessage('The email address is already in use. Please try logging in.');
                    break;
                case 'auth/too-many-requests':
                    setErrorMessage('Too many login attempts. Please try again later.');
                    break;
                default:
                    setErrorMessage('An unexpected error occurred. Please try again.');
                    console.error('Firebase Error:', error);
                    break;
            }
        }
    };

    const loginWithGoogleHandler = async () => {
        setErrorMessage(null);

        // Check if terms are accepted
        if (!termsAccepted) {
            setErrorMessage('You must accept the Terms and Conditions to continue');
            return;
        }

        try {
            await loginWithGoogle(linkProgressValue ?? false);
            // Check if there's a redirect URL stored
            const redirectUrl = localStorage.getItem('redirectAfterLogin');
            if (redirectUrl) {
                localStorage.removeItem('redirectAfterLogin'); // Clear it after use
                window.location.href = redirectUrl; // Use window.location for full page navigation
            } else {
                window.location.reload(); // Reload the page to update the user state
            }
        } catch (error: any) {
            switch (error.code) {
                case 'auth/popup-closed-by-user':
                    setErrorMessage('Google login popup was closed before signing in.');
                    break;
                case 'auth/cancelled-popup-request':
                    setErrorMessage('Only one Google login popup can be open at a time.');
                    break;
                case 'auth/network-request-failed':
                    setErrorMessage('Network error. Please check your internet connection.');
                    break;
                default:
                    setErrorMessage('An error occurred while signing in with Google.');
                    console.error('Google Sign-In Error:', error);
                    break;

            }
        }
    };

    return (
        <div className="flex items-center justify-center min-h-screen bg-[#FBFAFF] px-4 sm:px-8">
            <div className="p-8 bg-white rounded-lg shadow-md w-96 mx-auto border border-gray-300">
                <h1 className="text-2xl font-bold mb-6 text-center text-[#f4804e]">Sign Up</h1>

                {errorMessage && (
                    <div ref={errorMessageRef} className="mb-4 text-red-600 text-sm text-center">
                        {errorMessage}
                    </div>
                )}

                <form className="space-y-4 border-gray-300" onSubmit={handleSubmit(onSubmit)}>
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                            Name
                        </label>
                        <Input
                            type="text"
                            id="name"
                            {...register("name")}
                            className="mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        {errors.name && (
                            <p ref={nameErrorRef} className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                        )}
                    </div>

                    <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                            Email
                        </label>
                        <Input
                            type="email"
                            id="email"
                            {...register("email")}
                            className="mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        {errors.email && (
                            <p ref={emailErrorRef} className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                        )}
                    </div>

                    <div>
                        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                            Password
                        </label>
                        <Input
                            type="password"
                            id="password"
                            {...register("password")}
                            className="mt-1 border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        {errors.password && (
                            <p ref={passwordErrorRef}
                               className="text-red-500 text-sm mt-1">{errors.password.message}</p>
                        )}
                    </div>

                    <Controller
                        control={control}
                        name="linkProgress"
                        render={({field}) => (
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    className={"border-3 border-gray-600"}
                                    id="linkProgress"
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                />
                                <label htmlFor="linkProgress"
                                       className="text-sm cursor-pointer hover:cursor-pointer">
                                    Link my current progress with this account.
                                </label>
                            </div>
                        )}
                    />

                    <Controller
                        control={control}
                        name="acceptTerms"
                        render={({field}) => (
                            <div className="flex items-center space-x-2 hover:cursor-pointer">
                                <Checkbox
                                    className={"border-3 border-gray-600"}
                                    id="acceptTerms"
                                    checked={field.value}
                                    onCheckedChange={(checked) => {
                                        field.onChange(checked);
                                        // Update the external state for Google sign-in
                                        setTermsAccepted(checked === true);
                                    }}
                                />
                                <div className="text-sm">
                                    <span>I agree to the </span>
                                    <button
                                        type="button"
                                        onClick={() => setTermsDialogOpen(true)}
                                        className="text-[#E46F3E] font-semibold hover:underline cursor-pointer"
                                    >
                                        terms and conditions
                                    </button>
                                </div>
                            </div>
                        )}
                    />
                    {errors.acceptTerms && (
                        <p ref={termsErrorRef} className="text-red-500 text-sm mt-1">{errors.acceptTerms.message}</p>
                    )}

                    <Button
                        type="submit"
                        className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                    >
                        Sign Up
                    </Button>

                    <Separator className="my-4"/>

                    <Button
                        type="button"
                        variant="outline"
                        className="w-full border-gray-300 text-gray-700 active:bg-gray-100 transition-colors hover:cursor-pointer"
                        onClick={loginWithGoogleHandler}
                    >
                        <GoogleIcon className="h-4 w-4"/>
                        Sign in with Google
                    </Button>
                </form>

                <p className="mt-4 text-center text-sm text-gray-600">
                    Already have an account?{" "}
                    <Link
                        to="/login"
                        className="text-[#E46F3E] font-semibold hover:underline  cursor-pointer"
                    >
                        Sign in
                    </Link>
                </p>
            </div>

            <TermsAndConditionsDialog
                open={termsDialogOpen}
                onOpenChange={setTermsDialogOpen}
            />
        </div>
    );
}
