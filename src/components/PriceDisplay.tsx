import {Badge} from "@/components/ui/badge";
import {StripeCoupon, StripeProduct} from "@/utils/hooks/payments/interfaces";
import {motion} from "framer-motion";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";
import {InfoIcon} from "lucide-react";

interface PriceDisplayProps {
    product: StripeProduct;
    billingCycle: "monthly" | "yearly";
}

export function PriceDisplay({product, billingCycle}: PriceDisplayProps) {
    const price = product.prices.find(p =>
        billingCycle === "monthly" ? p.interval === "month" : p.interval === "year"
    );

    if (!price) return null;

    // Handle free products
    if (price.amount === 0) {
        return (
            <div className="mt-4 relative">
                <span className="text-4xl font-bold text-primary">Free</span>
                <span className="text-muted-foreground ml-2">forever</span>
                {price.trial_period_days > 0 && (
                    <div className="text-sm font-medium text-primary mt-2">
                        Includes {price.trial_period_days}-day trial
                    </div>
                )}
                <div className="absolute bottom-0 right-0">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <InfoIcon className="h-4 w-4 text-[#673AB7] hover:cursor-pointer hover:text-[#F4804D]"/>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="max-w-xs">Free plan with limited features</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </div>
        );
    }

    // Rest of your price display logic for paid products
    const bestCoupon = price.available_coupons.reduce((best, current) => {
        if (!best) return current;

        const currentDiscount = current.percent_off
            ? price.amount * (current.percent_off / 100)
            : current.amount_off || 0;

        const bestDiscount = best.percent_off
            ? price.amount * (best.percent_off / 100)
            : best.amount_off || 0;

        return currentDiscount > bestDiscount ? current : best;
    }, null as StripeCoupon | null);

    const finalPrice = price.discounted_amount ?? price.amount;
    const savings = price.amount - finalPrice;
    const savingsPercentage = Math.round((savings / price.amount) * 100);

    return (
        <div className="mt-4 space-y-2 relative">
            {product.has_discount && (
                <motion.div
                    initial={{scale: 0.5, opacity: 0}}
                    animate={{scale: 1, opacity: 1}}
                    transition={{
                        type: "spring",
                        stiffness: 260,
                        damping: 20
                    }}
                    className="flex items-center gap-2"
                >
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Save {savingsPercentage}%
                    </Badge>
                    {bestCoupon?.duration === "repeating" && (
                        <span className="text-sm text-muted-foreground">
                            for {bestCoupon.duration_in_months} months
                        </span>
                    )}
                </motion.div>
            )}
            {billingCycle === "monthly" ? (
                <div className="flex items-baseline gap-2">
                    {product.has_discount && (
                        <span className="text-xl line-through text-muted-foreground">
                            ${price.amount}
                        </span>
                    )}
                    <span className="text-4xl font-bold text-primary">
                        ${finalPrice.toFixed(2)}
                    </span>
                    <span className="text-muted-foreground">/mo</span>
                </div>
            ) : (
                <div className="space-y-1">
                    <div className="flex items-baseline gap-2">
                        {product.has_discount && (
                            <span className="text-xl line-through text-muted-foreground">
                                ${(price.amount / 12).toFixed(2)}
                            </span>
                        )}
                        <span className="text-4xl font-bold text-primary">
                            ${(finalPrice / 12).toFixed(2)}
                        </span>
                        <span className="text-muted-foreground">/mo</span>
                    </div>
                    <div className="text-sm font-medium text-muted-foreground">
                        ${finalPrice.toFixed(2)} billed annually
                    </div>
                </div>
            )}
            {price.trial_period_days > 0 && (
                <div className="text-sm font-medium text-primary">
                    Includes {price.trial_period_days}-day free trial
                </div>
            )}
            <div className="absolute bottom-0 right-0">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger>
                            <InfoIcon className="h-4 w-4 text-[#673AB7] hover:cursor-pointer hover:text-[#F4804D]"/>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p className="max-w-xs">Credit quota resets {billingCycle === "monthly" ? "every month" : "annually"} from your subscription
                                date. If you need more credits you can buy extra any time.</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </div>
    );
}