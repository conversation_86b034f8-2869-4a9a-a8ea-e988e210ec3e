import {useEffect, useState} from "react";
import {Dialog, DialogContent} from "@/components/ui/dialog.tsx";
import {Progress} from "@/components/ui/progress.tsx";
import CustomLoader from "@/components/ui/CustomLoader.tsx";

interface LoaderModalProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    loadingTexts?: string[];
    duration?: number;
    showProgress?: boolean;
    progress?: number;
}

export function FuseVideoLoadingModal({
                                          isOpen,
                                          onOpenChange,
                                          loadingTexts = ["Loading resources...", "Preparing data...", "Almost there...", "Finalizing..."],
                                          duration = 15000,
                                          showProgress = true,
                                          progress,
                                      }: LoaderModalProps) {

    const [currentTextIndex, setCurrentTextIndex] = useState(0);
    const [progressValue, setProgressValue] = useState(0);
    const [startTime, setStartTime] = useState<number | null>(null);


useEffect(() => {
    if (!isOpen) {
        setProgressValue(0);
        setStartTime(null);
        return;
    }

    if (progress === 100) {
        setProgressValue(100);
        onOpenChange(false);
        return;
    }

    // Clear existing intervals
    let progressInterval: NodeJS.Timeout | null = null;
    let textInterval: NodeJS.Timeout | null = null;

    // Only run timer-based progress if no progress prop provided
    if (progress === undefined) {
        const now = Date.now();
        setStartTime(now);

        progressInterval = setInterval(() => {
            if (startTime) {
                const elapsed = Date.now() - startTime;
                const newProgress = Math.min((elapsed / duration) * 100, 100);
                setProgressValue(newProgress);

                if (newProgress >= 100) {
                    onOpenChange(false);
                }
            }
        }, 100);
    } else {
        // Sync with parent-provided progress
        setProgressValue(progress);
    }

    // Update loading text regardless of progress source
    textInterval = setInterval(() => {
        setCurrentTextIndex((prev) => (prev + 1) % loadingTexts.length);
    }, 1500);

    return () => {
        progressInterval && clearInterval(progressInterval);
        textInterval && clearInterval(textInterval);
    };
}, [isOpen, loadingTexts.length, startTime, duration, progress, onOpenChange]);
    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <div className="flex flex-col items-center justify-center space-y-6 py-6">
                    <div className="relative flex h-24 w-24 items-center justify-center">
                        <CustomLoader color="#673AB7" className="bg-transparent mt-0 py-0"/>
                    </div>

                    <div className="text-display-container relative h-16 w-full overflow-hidden">
                        {loadingTexts.map((text, index) => (
                            <div
                                key={index}
                                className={`text-item absolute left-0 top-0 flex h-full w-full items-center justify-center text-center transition-all duration-500 ${
                                    index === currentTextIndex
                                        ? "opacity-100 transform-none"
                                        : index === (currentTextIndex + 1) % loadingTexts.length
                                            ? "opacity-0 translate-y-full rotate-12"
                                            : "opacity-0 -translate-y-full -rotate-12"
                                }`}
                            >
                                <span className="text-lg font-medium">{text}</span>
                            </div>
                        ))}
                    </div>

                    {showProgress && (
                        <div className="w-full max-w-xs">
                            <Progress value={progressValue} className="h-2"/>
                            <p className="mt-2 text-center text-sm text-muted-foreground">{Math.round(progressValue)}%
                                complete</p>
                        </div>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
}
