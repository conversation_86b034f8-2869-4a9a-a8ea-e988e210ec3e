import {useGenerateQrCode} from "@/utils/hooks/video/useGenerateQrCode.ts";
import {Loader2, Languages} from "lucide-react";
import {useState} from "react";
import {Select, SelectContent, SelectItem, SelectTrigger} from "@/components/ui/select";
import {leftColumnInstructions, rightColumnInstructions} from "@/lib/scripts.ts";
import ReactMarkdown from "react-markdown";

export default function MagicScriptModal() {
    const {data, isLoading, isError, refetch} = useGenerateQrCode();
    const [currentLanguage, setCurrentLanguage] = useState<'en' | 'hi' | 'hi-romanized' | 'es_general' | 'es_mx' | 'ar' | 'ur' | 'ur-romanized' | 'fr'>('en');
    const currentInstructions = leftColumnInstructions[currentLanguage];
    const currentRightColumnInstructions = rightColumnInstructions[currentLanguage];

    // Handle language change
    const handleLanguageChange = (newLanguage: 'en' | 'hi' | 'hi-romanized' | 'es_general' | 'es_mx' | 'ar' | 'ur' | 'ur-romanized' | 'fr') => setCurrentLanguage(newLanguage);

    return (
        <div
            className="flex flex-col h-full max-h-[70vh] overflow-y-auto pb-4 w-full scrollbar-thin scroll-bar-thumb-[#673AB7] scrollbar-thumb-rounded-full scrollbar-track-transparent">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border-gray-200 px-2 sm:px-4 overflow-y-auto h-full">
                {/* Left Column - Script and QR Code */}
                <div className="border-2 border-gray-300 p-2 sm:p-4 rounded-md relative">
                    <div className="absolute top-2 right-2 hover:text-white hover:cursor-pointer">
                        <Select value={currentLanguage} onValueChange={handleLanguageChange}>
                            <SelectTrigger
                                className="w-auto bg-[#F0EBF8] border border-[#673AB7] p-2 text-sm focus:outline-none hover:cursor-pointer  hover:text-white hover:bg-[#673AB7] rounded-full transition-colors"
                                aria-label="Change language">
                                <span className="flex items-center"><Languages className="h-5 w-5 hover:text-white"/>
                                </span>
                            </SelectTrigger>
                            <SelectContent className="bg-white border border-[#673AB7]">
                                <SelectItem value="en"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">English</SelectItem>
                                <SelectItem value="fr"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">Français</SelectItem>
                                <SelectItem value="hi"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">हिन्दी</SelectItem>
                                <SelectItem value="ur"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">اردو</SelectItem>
                                <SelectItem value="ar"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">العربية</SelectItem>
                                <SelectItem value="hi-romanized"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">Hindi
                                    (Romanized)</SelectItem>
                                <SelectItem value="ur-romanized"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">Urdu
                                    (Romanized)</SelectItem>
                                <SelectItem value="es_general"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">Español
                                    (General)</SelectItem>
                                <SelectItem value="es_mx"
                                            className="text-[#673AB7] hover:bg-[#673AB7] hover:text-white">Español
                                    (México)</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    <h3 className="text-lg font-semibold text-[#673AB7] mb-4 text-center">Script</h3>
                    <div
                        className="max-h-[40vh] overflow-y-auto bg-[#F7F5FB] p-2 sm:p-4 rounded-md space-y-3 sm:space-y-4 border border-[#F0EBF8] relative scrollbar-thin scrollbar-thumb-[#673AB7] scrollbar-track-transparent">
                        {currentInstructions.map((item, index) => (
                            <div key={index}>
                                <h3 className="text-base sm:text-lg font-semibold">{item.heading}</h3>
                                <p className="text-gray-700 text-sm sm:text-base">{item.script}</p>
                            </div>
                        ))}
                    </div>
                    <div className="mt-4 text-center">
                        <p className="font-medium mb-2">Scan this QR code to view the script on an other device or
                            <a href={"/script"} target="_blank" rel="noopener noreferrer" className="text-[#E46F3E] hover:underline  cursor-pointer"> click here </a>
                            to view it on another tab</p>
                        <div className="mx-auto h-24 w-24 sm:h-32 sm:w-32 flex items-center justify-center my-2">
                            {isLoading && <Loader2 className="animate-spin text-gray-500 w-8 h-8"/>}
                            {isError && (
                                <div className="text-red-500">
                                    Error loading QR code. <button onClick={() => refetch()}
                                                                   className="text-blue-500 underline ml-1">Retry</button>
                                </div>
                            )}
                            {data && !isLoading && !isError &&
                                <img src={data.qr_code} alt="QR Code" className="w-full h-full object-contain"/>}
                        </div>
                    </div>
                </div>

                {/* Right Column - Instructions */}
                <div className="flex flex-col h-full p-2 sm:p-0">
                    <div className="bg-[#F7F5FB] p-4 rounded-md border border-[#F0EBF8] h-full">
                        <h3 className="text-lg font-semibold text-[#673AB7] mb-4 text-center">Instructions</h3>
                        <div className="space-y-3 prose prose-sm max-w-none">
                            {currentRightColumnInstructions.map((instruction, index) => (
                                <div key={index} className="text-sm text-gray-700">
                                    <ReactMarkdown
                                        components={{
                                            strong: ({node, ...props}) => <span
                                                className="font-bold text-[#673AB7]" {...props} />,
                                            li: ({node, ...props}) => <li className="ml-5 pl-1" {...props} />,
                                            ul: ({node, ...props}) => <ul
                                                className="list-disc ml-2 space-y-1" {...props} />
                                        }}
                                    >
                                        {instruction}
                                    </ReactMarkdown>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
