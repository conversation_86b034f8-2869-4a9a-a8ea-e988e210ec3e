import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
    Dialog,
    DialogContent, DialogDescription,
    DialogFooter,
    DialogTitle,
} from "@/components/ui/dialog"
import {} from "@radix-ui/react-dialog";
import {UserCircle2} from "lucide-react";

interface AnonymousLoginModalProps {
    onContinue: (name: string) => void
}

export function AnonymousLoginModal({ onContinue }: AnonymousLoginModalProps) {
    const [open, setOpen] = useState(false)
    const [name, setName] = useState("")
    const [error, setError] = useState("")

    const handleContinue = () => {
        if (!name.trim()) {
            setError("Please enter a name to continue")
            return
        }

        onContinue(name)
        setOpen(false)
        setName("")
        setError("")
    }

    return (
        <>
            <Button variant="outline" className="w-full mt-4 border-gray-300 text-gray-600 hover:text-gray-900 hover:cursor-pointer" onClick={() => setOpen(true)}>
                <UserCircle2 className="mr-2 h-4 w-4" />
                Continue anonymously
            </Button>

            <Dialog open={open} onOpenChange={setOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogTitle>
                        <DialogTitle>Continue Anonymously</DialogTitle>
                        <DialogDescription>Enter a display name to continue without creating an account.</DialogDescription>
                    </DialogTitle>

                    <div className="py-4">
                        <label htmlFor="anonymous-name" className="block text-sm font-medium text-gray-700 mb-1">
                            Display Name
                        </label>
                        <Input
                            id="anonymous-name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            placeholder="Enter your name"
                            className="w-full"
                        />
                        {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
                    </div>

                    <DialogFooter>
                        <Button variant="outline" onClick={() => setOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleContinue}>Continue</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}

