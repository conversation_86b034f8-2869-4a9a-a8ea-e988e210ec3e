import {useEffect, useRef, useState} from "react";
import {But<PERSON>} from "@/components/ui/button.tsx";
import {useAuth} from "@/auth/use-auth.ts";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog.tsx";

interface EmailVerificationModalProps {
    isOpen: boolean;
    onOpenChange?: (open: boolean) => void;
}

export default function EmailVerificationModal({isOpen, onOpenChange}: EmailVerificationModalProps) {
    const {user, sendVerificationEmail, reloadUser, logout} = useAuth();
    const [message, setMessage] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [isResending, setIsResending] = useState(false);
    const messageRef = useRef<HTMLDivElement>(null);

    // Check verification status periodically
    useEffect(() => {
        if (!isOpen || !user) return;

        // If user is already verified, close modal
        if (user.emailVerified) {
            onOpenChange?.(false);
            return;
        }

        // Set up interval to check verification status
        const interval = setInterval(async () => {
            try {
                const updatedUser = await reloadUser();
                if (updatedUser.emailVerified) {
                    onOpenChange?.(false);
                    // Reload the page to update the user state everywhere
                    window.location.reload();
                }
            } catch (error) {
                console.error('Error checking verification status:', error);
            }
        }, 3000); // Check every 3 seconds

        return () => clearInterval(interval);
    }, [isOpen, user, reloadUser, onOpenChange]);

    useEffect(() => {
        if (message && messageRef.current) {
            messageRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }, [message]);

    const handleResend = async () => {
        setMessage(null);
        setError(null);
        setIsResending(true);
        try {
            await sendVerificationEmail();
            setMessage('Verification email resent. Please check your inbox.');
        } catch (err: any) {
            setError('Failed to resend verification email. Please try again later.');
            console.error('Resend Email Verification Error:', err);
        } finally {
            setIsResending(false);
        }
    };

    const handleLogout = async () => {
        try {
            await logout();
        } catch (error) {
            console.error('Error logging out:', error);
        }
    };

    const handleOpenChange = (open: boolean) => {
        // Prevent closing the modal manually - user must verify or logout
        if (!open) return;
        onOpenChange?.(open);
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
            <DialogContent className="sm:max-w-[425px]" hideCloseButton>
                <DialogHeader>
                    <DialogTitle className="text-2xl font-bold text-center text-[#f4804e]">
                        Verify Your Email
                    </DialogTitle>
                    <DialogDescription className="text-center">
                        We've sent a verification link to your email address.
                    </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                    {message && (
                        <div
                            ref={messageRef}
                            className="mb-4 text-green-600 text-sm text-center"
                        >
                            {message}
                        </div>
                    )}

                    {error && (
                        <div
                            ref={messageRef}
                            className="mb-4 text-red-600 text-sm text-center"
                        >
                            {error}
                        </div>
                    )}

                    <p className="text-gray-700 mb-4 text-center">
                        Please check your inbox and click the link to verify your account.
                    </p>

                    <p className="text-sm text-gray-500 mb-6 text-center">
                        This dialog will automatically close once your email is verified.
                    </p>

                    <div className="space-y-3">
                        <Button
                            onClick={handleResend}
                            disabled={isResending}
                            className="w-full text-center px-3 py-2 text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                        >
                            {isResending ? 'Sending...' : 'Resend Verification Email'}
                        </Button>

                        <Button
                            variant="outline"
                            onClick={handleLogout}
                            className="w-full border-gray-300 text-gray-700 hover:bg-gray-100"
                        >
                            Log Out
                        </Button>
                    </div>

                    <p className="mt-4 text-center text-sm text-gray-600">
                        Already verified? The page will refresh automatically.
                    </p>
                </div>
            </DialogContent>
        </Dialog>
    );
}
