import {But<PERSON>} from "@/components/ui/button.tsx";
import {Input} from "@/components/ui/input.tsx";
import {useEffect, useState, useRef} from "react";
import Cameras from "@/components/Camera.tsx";
import {Link} from "@tanstack/react-router";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogFooter,
    DialogTitle,
    DialogTrigger,
} from "./ui/dialog";
import MagicScriptModal from "./MagicScriptModal";
import RecommendedVideoTypes from "./RecommendedVideoTypes";
import SampleVideosModal, {formatDuration} from "@/components/SampleVideosModal.tsx";
import {VideoReturnDTO} from "@/utils/hooks/video/useSampleVideos";
import {useYoutubeInfo, YoutubeVideoInfo} from "@/utils/hooks/video/useYoutubeInfo.ts";
import {usePresignedUrl} from "@/utils/hooks/video/usePresignedUrl.ts";
import {MAX_DURATION, MIN_DURATION} from "@/utils/constants.ts";
import {GetUploadUrlResponse, useGetUploadUrl} from "@/utils/hooks/video/useUploadUrl.ts";
import {uploadImageToS3, uploadVideoToS3} from "@/utils/hooks/video/uploadVideoToS3.ts";
import {useProcessVideo} from "@/utils/hooks/video/useProcessVideo.ts";
import {Separator} from "@radix-ui/react-separator";
import {JobStatus, useCheckJobStatus} from "@/utils/hooks/video/useVideoProcessing.ts";
import {triggerEvent} from "@/components/PageTracking.tsx";
import Language from "@/components/Language.tsx";
import NotEnoughCreditsModal from "@/components/NotEnoughCreditsModal.tsx";
import {InfoIcon, Link as LinkIcon} from "lucide-react";
import {Label} from "@/components/ui/label.tsx";
import {ClipLoader} from "react-spinners";
import {SparklesIcon} from "@/components/ui/SparklesIcon.tsx";
import {Progress} from "@/components/ui/progress.tsx";
import {Card, CardContent, CardFooter, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip.tsx";
import {getVideoDuration, getVideoOrientation, getVideoThumbnail} from "@/lib/video.ts";

export default function Playground() {
    const [personalVideo, setPersonalVideo] = useState<File | null>(null);
    const [personalVideoPreviewUrl, setPersonalVideoPreviewUrl] = useState<string | null>(null);
    const [personalVideoDuration, setPersonalVideoDuration] = useState<string>("");
    const [error, setError] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [youtubeURL, setYoutubeURL] = useState("");
    const [recordedVideo, setRecordedVideo] = useState<Blob | null>(null);
    const [showCamera, setShowCamera] = useState(false);
    const [youTubeVideoInfo, setYouTubeVideoInfo] = useState<YoutubeVideoInfo | undefined>({
        video_id: '',
        title: '',
        thumbnail: '',
        duration: '',
        exceeds_limit: false,
    });
    const [s3Key, setS3Key] = useState<string>('');
    const [processedVideoID, setProcessedVideoID] = useState("");
    const [personalSampleVideoSelected, setPersonalSampleVideosSelected] = useState<VideoReturnDTO | null>(null);
    const [isProcessingModalOpen, setIsProcessingModalOpen] = useState(!!processedVideoID);
    const [selectedLanguage, setSelectedLanguage] = useState(localStorage.getItem('language') || 'en');
    const [progressPercentage, setProgressPercentage] = useState(0);
    const [processingMessages] = useState([
        "Please wait while your video is being prepared... good things take time!",
        "Feel free to switch tabs, but don't let your device take a nap!",
        "Analyzing your video to capture your unique voice and style.",
        "Remember: The preview won't have perfect lip sync. For the real magic, join our waitlist!",
        "Analyzing your video to capture your unique voice and style.",
        "Fun fact: The first-ever video uploaded to YouTube was about an elephant! 🐘",
        "Scanning the reference video—extracting the best bits like an AI detective.",
        "Feel free to switch tabs, but don't let your device take a nap!",
        "Scanning the reference video—extracting the best bits like an AI detective.",
        "Pro tip: Good things take time, great things take AI! 😉",
        "Summarizing the topic and crafting a fresh, engaging script.",
        "Now cloning you in the video—don't worry, no evil twin involved.",
        "Feel free to switch tabs, but don't let your device take a nap!",
        "Adding subtitles—because who doesn't love karaoke mode?",
        "If you stare at this progress bar long enough, it might just move faster.",
        "Final touches—this is where the AI sprinkles its fairy dust.",
        "Just a little longer… Rome wasn't built in a day, but your video will be done in minutes!",
        "Feel free to switch tabs, but don't let your device take a nap!",
        "AI is working its magic—meanwhile, maybe do some stretches?",
        "Your video is almost ready... but can AI make popcorn too?",
        "Remember: The preview won't have perfect lip sync. For the real magic, join our waitlist!",
        "This is the part where AI flexes its muscles. Processing…",
        "Hold tight! We're making sure this video is worth the wait."
    ]);
    const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
    const videoRef = useRef<HTMLVideoElement>(null);
    const progressRef = useRef<HTMLDivElement>(null);
    const progressTimerRef = useRef<NodeJS.Timeout | null>(null);
    const messageRotationTimerRef = useRef<NodeJS.Timeout | null>(null);


    const {data: jobStatusData, error: jobStatusError} = useCheckJobStatus(processedVideoID);

    useEffect(() => {
        const fetchData = async () => {
            if (s3Key && youtubeURL)
                await processVideo(s3Key, youtubeURL, selectedLanguage);
        };
        void fetchData()

    }, [s3Key, youtubeURL, selectedLanguage]);


    // remove error message on change of personal video or youtube url or personal sample video selected
    useEffect(() => {
        setError(false);
        setErrorMessage(null);
    }, [personalVideo, youtubeURL, personalSampleVideoSelected]);

    //  open processing modal when the video is being processed
    useEffect(() => {
        if (jobStatusData?.status === JobStatus.COMPLETED) {
            setIsProcessingModalOpen(false);
            // Clear all timers
            if (progressTimerRef.current) {
                clearInterval(progressTimerRef.current);
                progressTimerRef.current = null;
            }
            if (messageRotationTimerRef.current) {
                clearInterval(messageRotationTimerRef.current);
                messageRotationTimerRef.current = null;
            }
        } else if (jobStatusData?.status === "FAILED" || jobStatusError) {
            setIsProcessingModalOpen(false);
            setError(true);
            setErrorMessage("Failed to process video, Please try again");
            // Clear all timers
            if (progressTimerRef.current) {
                clearInterval(progressTimerRef.current);
                progressTimerRef.current = null;
            }
            if (messageRotationTimerRef.current) {
                clearInterval(messageRotationTimerRef.current);
                messageRotationTimerRef.current = null;
            }
        }
    }, [jobStatusData, processedVideoID, jobStatusError]);


    // Progress timer effect
    useEffect(() => {
        // Start the progress timer when the processing modal is opened
        if (isProcessingModalOpen) {
            // Reset progress percentage
            setProgressPercentage(0);
            // Reset message index
            setCurrentMessageIndex(0);

            // Clear any existing timers
            if (progressTimerRef.current) {
                clearInterval(progressTimerRef.current);
            }
            if (messageRotationTimerRef.current) {
                clearInterval(messageRotationTimerRef.current);
            }

            // Calculate the increment needed to reach 100% in 10 minutes
            // Update every 3 seconds, so we need 200 updates to reach 100% in 10 minutes
            const totalUpdates = 200;
            const incrementPerUpdate = 100 / totalUpdates;

            // Start the progress timer
            progressTimerRef.current = setInterval(() => {
                setProgressPercentage(prev => {
                    // If job is already completed, set to 100%
                    if (jobStatusData?.status === JobStatus.COMPLETED) {
                        return 100;
                    }

                    // Cap at 99% until we get confirmation of completion
                    const newValue = prev + incrementPerUpdate;
                    return newValue >= 99 ? 99 : newValue;
                });
            }, 3000); // Update every 3 seconds

            // Start the message rotation timer
            messageRotationTimerRef.current = setInterval(() => {
                setCurrentMessageIndex(prevIndex => {
                    // If we're at the last message, stay there
                    if (prevIndex === processingMessages.length - 1) {
                        return prevIndex;
                    }
                    return prevIndex + 1;
                });
            }, 15000);
            return () => {
                // Clean up both timers
                if (progressTimerRef.current) {
                    clearInterval(progressTimerRef.current);
                    progressTimerRef.current = null;
                }
                if (messageRotationTimerRef.current) {
                    clearInterval(messageRotationTimerRef.current);
                    messageRotationTimerRef.current = null;
                }
            };
        }
    }, [isProcessingModalOpen, jobStatusData?.status, processingMessages.length]);

    // Auto-scroll to progress bar or video based on job status
    useEffect(() => {
        if (jobStatusData?.status === JobStatus.COMPLETED && jobStatusData.downloadUrl && videoRef.current) {
            // Scroll to video when processing is complete
            videoRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        } else if ((jobStatusData?.status === JobStatus.PENDING || jobStatusData?.status === JobStatus.PROCESSING) && progressRef.current) {
            // Scroll to progress bar when processing is in progress
            progressRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }, [jobStatusData?.status, jobStatusData?.downloadUrl]);


    // Function to handle video recording completion
    const handleVideoCapture = (videoBlob: Blob) => {
        // Clear other video sources when recording a video
        setPersonalVideo(null);
        setPersonalVideoPreviewUrl(null);
        setPersonalVideoDuration("");
        setPersonalSampleVideosSelected(null);

        setRecordedVideo(videoBlob);
        setShowCamera(false);
        triggerEvent({eventName: 'video_recorded', page_section: 'body'})
    };

    const {data: YoutubeVideoInfo} = useYoutubeInfo(youtubeURL);
    useEffect(() => {
        if (YoutubeVideoInfo) {
            setYouTubeVideoInfo(YoutubeVideoInfo);
        }
    }, [YoutubeVideoInfo]);

    const HandleReferenceVideoSelect = (video: VideoReturnDTO) => {
        const youtubeURL = `https://www.youtube.com/watch?v=${video.video_id}`;
        setYoutubeURL(youtubeURL);
        triggerEvent({eventName: 'clicked_reference_video', page_section: 'body'})

    }

    const filterSampleVideoPreviewPresignedUrl = (video: VideoReturnDTO | null): string => {
        if (video) {
            const sampleVideoName = `${video.video_id}.${video.title.split('.').pop()}`
            return `user-uploads/${sampleVideoName}`
        }
        return "";
    }

    const {
        data: personalSampleVideoPresigned,
    } = usePresignedUrl(filterSampleVideoPreviewPresignedUrl(personalSampleVideoSelected));


    const HandlePersonalVideoSelect = (video: VideoReturnDTO) => {
        // Clear other video sources when selecting a sample video
        setPersonalVideo(null);
        setPersonalVideoPreviewUrl(null);
        setPersonalVideoDuration("");
        setRecordedVideo(null);
        setShowCamera(false);
        // Set the selected sample video
        setPersonalSampleVideosSelected(video);
        triggerEvent({eventName: 'clicked_personal_video', page_section: 'body'})
    }

    const {mutate: fetchUploadUrl} = useGetUploadUrl();

    const handleFuseButtonCLick = async () => {
        setS3Key("") // reset s3 key
        setProgressPercentage(0) // reset progress percentage
        triggerEvent({eventName: 'clicked_fuse_videos', page_section: 'body'})
        try {
            if (!recordedVideo && !personalVideo && !personalSampleVideoSelected) {
                console.log("Error")
                setErrorMessage("Please upload or record a personal video");
                setError(true);
            }

            if (!youtubeURL) {
                setErrorMessage('Please enter a YouTube URL.');
                setError(true)
            }

            if (personalVideo) {
                const duration = await getVideoDuration(personalVideo);
                const orientation = await getVideoOrientation(personalVideo);
                if (duration < MIN_DURATION || duration > MAX_DURATION) {
                    setErrorMessage('Video duration must be between the allowed limits.');
                    setError(true);
                }
                if (orientation === 'portrait') {
                    setErrorMessage('Please upload a square or landscape video. Portrait is not supported.');
                    setError(true)
                }
                const thumbnail = await getVideoThumbnail(personalVideo);
                try {
                    fetchUploadUrl({
                        fileName: personalVideo.name.replace(/ /g, '-'),
                        fileType: personalVideo.type,
                        duration: String(duration),
                    }, {
                        onSuccess: async (data: GetUploadUrlResponse) => {
                            if (data.uploadUrl !== "already_uploaded") {
                                await uploadVideoToS3(personalVideo, data.uploadUrl);
                                await uploadImageToS3(thumbnail, personalVideo.name, String(duration));
                            }
                            setS3Key(data.s3Key)
                        },
                        onError: (error) => {
                            console.log(error);
                        }
                    });
                } catch {
                    setErrorMessage('Failed to get upload URL.');
                    setError(true);
                }
            } else if (personalSampleVideoSelected) {
                setS3Key("user-uploads/" + personalSampleVideoSelected?.video_id);
            } else if (recordedVideo) {
                //     convert blob into file and upload to s3 and get the s3 key
                const file = new File([recordedVideo], 'PV-' + Math.floor(10000 + Math.random() * 90000) + ".webm", {type: 'video/webm'});
                const duration = await getVideoDuration(file);
                const orientation = await getVideoOrientation(file);
                if (duration < MIN_DURATION || duration > MAX_DURATION) {
                    setErrorMessage('Video duration must be between the allowed limits.');
                    setError(true);
                    triggerEvent({eventName: 'fuse_video_duration_error', page_section: 'body'})
                }
                if (orientation === 'portrait') {
                    setErrorMessage('Please upload a square or landscape video. Portrait is not supported.');
                    setError(true)
                    triggerEvent({eventName: 'fuse_video_orientation_error', page_section: 'body'})
                }
                const thumbnail = await getVideoThumbnail(file);
                try {
                    fetchUploadUrl({
                        fileName: file.name.replace(/ /g, '-'),
                        fileType: file.type,
                        duration: String(duration),
                    }, {
                        onSuccess: async (data: GetUploadUrlResponse) => {
                            if (data.uploadUrl !== "already_uploaded") {
                                await uploadVideoToS3(file, data.uploadUrl);
                                await uploadImageToS3(thumbnail, file.name, String(duration));
                            }
                            setS3Key(data.s3Key)
                        },
                        onError: (error) => {
                            console.log(error);
                            setErrorMessage('Failed to get upload URL.');
                            setError(true);
                            triggerEvent({eventName: 'fuse_video_upload_error', page_section: 'body'})
                        }
                    });
                } catch {
                    setErrorMessage('Failed to get upload URL.');
                    setError(true);
                    triggerEvent({eventName: 'fuse_video_upload_error', page_section: 'body'})

                }
            }
        } catch {
            console.log("Error")
        }
    };

    const {
        mutateAsync,
        isPending,
        isError: processingVideoError,
        showNotEnoughCreditsDialog,
        resetNotEnoughCreditsDialog,
    } = useProcessVideo();


    const processVideo = async (s3Key: string, youtubeUrl: string, language: string) => {
        if (isPending) {
            return;
        }
        if (processingVideoError) {
            setErrorMessage('Processing video error');
            setError(true);
            return;
        }

        // Reset progress percentage when starting a new video processing
        setProgressPercentage(0);

        try {
            mutateAsync({
                personal_video_s3_key: s3Key,
                youtube_url: youtubeUrl,
                language: language,
                length: 50
            }).then(
                (data) => {
                    setProcessedVideoID(data.video_id);
                    setIsProcessingModalOpen(true);
                    triggerEvent({eventName: 'fuse_video_success', page_section: 'body'})
                }
            )
        } catch {
            setErrorMessage('Failed to process video');
            setError(true);
        }

    }

    const extractYouTubeVideoId = (url: string) => {
        const match = url.match(/(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([^&]+)/);
        return match ? match[1] : '';
    }

    const validateYouTubeUrl = (url: string) => {
        const pattern = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(\S+)?$/;
        return pattern.test(url);
    };

    const handleLanguageSelect = (lang: string) => {
        setSelectedLanguage(lang);
        triggerEvent({eventName: 'language_selected', page_section: 'body'})
    }

    return (
        <div className="container mx-auto py-10 px-4 sm:px-6 md:px-8">
            <h1 className="text-4xl font-bold text-center text-[#F4804D]">
                Video Fusion Preview
            </h1>
            <p className="text-3xl text-center my-3 text-gray-700 font-medium hidden md:block">
                Borrow ideas from any video and bring them into your own in minutes!
            </p>
            <p className="text-lg text-center text-gray-700 max-w-5xl mx-auto hidden md:block">
                Get a taste of Clones with our Video Fusion Preview. If you like any video on the internet, you can now
                create a similar video featuring you.
                Try our Video Fusion tester below, and
                and{" "}
                <Link to="/signup" className="text-[#E46F3E] font-semibold hover:underline  cursor-pointer">
                    sign-up
                </Link>{" "}
                to get full access to all our video generation features!
            </p>
            <div className="max-w-4xl mx-auto text-lg mt-2">
                <p>Here are 3 quick steps to use the tester:</p>
                <ul className="list-disc list-inside my-2 ">
                    <li>
                        <span className="font-bold">Step 1: </span>Select a face from our samples or upload a 1-minute
                        video of you. Use our{" "}
                        <Dialog>
                            <DialogTrigger className="text-[#E46F3E] font-semibold hover:underline  cursor-pointer">
                                magic script
                            </DialogTrigger>
                            <DialogContent title="Magic Script" className={"min-w-1/2 "}>
                                <DialogTitle>Magic Script</DialogTitle>
                                <MagicScriptModal/>
                                <DialogFooter>
                                    <DialogClose asChild>
                                        <Button
                                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                            aria-label="Close"
                                        >
                                            Close
                                        </Button>
                                    </DialogClose>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>
                    </li>
                    <li>
                        <span className="font-bold">Step 2: </span>Paste any YouTube video link. For best results
                        follow{" "}
                        <Dialog>
                            <DialogTrigger className="text-[#E46F3E] font-semibold hover:underline  cursor-pointer">
                                recommended video types
                            </DialogTrigger>
                            <DialogContent title="Recommended Video Types" className={"min-w-1/2"}>
                                <DialogTitle>Recommended Video Types</DialogTitle>
                                <RecommendedVideoTypes/>
                                <DialogFooter>
                                    <DialogClose asChild>
                                        <Button
                                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                            aria-label="Close"
                                        >
                                            Close
                                        </Button>
                                    </DialogClose>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>
                    </li>
                    <li>
                        <span className="font-bold">Step 3: </span>Hit the "Fuse Videos"
                        button and wait for 8 - 10 minutes
                    </li>
                </ul>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 max-w-6xl mx-auto gap-6 mt-4">
                {/* Personal Video Upload */}
                <div className="p-6 bg-[#F0EBF8] shadow-xl rounded-xl ">
                    <div className="flex items-center gap-2 text-[#673AB7] bg-transparent ">
                        <h5 className=" mx-auto text-lg font-semibold -mt-2 -mb-1 ">Face Video</h5>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <InfoIcon className="w-5 h-5 hover:cursor-pointer hover:text-[#F4804D]"/>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p className="max-w-xs">Use a sample face video or record your face video
                                        <br/>
                                        * Record a 1-min video of yourself
                                        <br/>
                                        * Keep camera horizontal (landscape mode)
                                        <br/>
                                        * Use the magic script or use your own words
                                    </p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Separator className="my-4 bg-gray-300"/>
                    <div className="flex bg-white py-1 items-center rounded-sm gap-1 px-2 border border-gray-400">
                        <Label className="relative cursor-pointer">
                            <span
                                className="bg-gray-200 text-gray-700 py-2 px-4 rounded-sm inline-flex items-center justify-center w-[100px] h-9 whitespace-nowrap">
                                Choose File
                            </span>
                            <Input
                                type="file"
                                accept="video/*"
                                onChange={async (e) => {
                                    const file = e.target.files ? e.target.files[0] : null;
                                    // Clear other video sources when uploading a personal video
                                    setPersonalSampleVideosSelected(null);
                                    setRecordedVideo(null);
                                    setPersonalVideo(file);
                                    setShowCamera(false);
                                    // Create preview URL for the uploaded video
                                    if (file) {
                                        const url = URL.createObjectURL(file);
                                        setPersonalVideoPreviewUrl(url);
                                        // Calculate and set the duration
                                        try {
                                            const durationInSeconds = await getVideoDuration(file);
                                            setPersonalVideoDuration(formatDuration(String(durationInSeconds)));
                                        } catch (error) {
                                            console.error("Error calculating video duration:", error);
                                            setPersonalVideoDuration("");
                                        }
                                    } else {
                                        setPersonalVideoPreviewUrl(null);
                                        setPersonalVideoDuration("");
                                    }
                                }}
                                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            />
                        </Label>
                        <p className="truncate text-gray-500  w-full ml-1">
                            {personalVideo?.name || "No file chosen"}
                        </p>
                    </div>

                    <div className="flex flex-wrap gap-2 mt-3">
                        <Button
                            variant="outline"
                            onClick={() => {
                                setPersonalVideo(null);
                                setPersonalVideoPreviewUrl(null);
                                setPersonalVideoDuration("");
                                setPersonalSampleVideosSelected(null);
                                setShowCamera(true)
                            }
                            }
                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                        >
                            Record Video
                        </Button>
                        <SampleVideosModal videoType={"personal"} onSelectVideo={HandlePersonalVideoSelect}/>
                    </div>

                    {personalSampleVideoPresigned && (
                        <div className="mt-4">
                            <h5 className="font-semibold">Sample Face Video</h5>
                            <p>Title: {personalSampleVideoSelected?.title}</p>
                            <p>Duration: {formatDuration(personalSampleVideoSelected?.duration ?? "")}</p>
                            <video
                                src={personalSampleVideoPresigned.downloadUrl}
                                poster={personalSampleVideoSelected?.thumbnail}
                                style={{height: '280px', width: '100%', objectFit: 'contain'}}
                                className="mt-2 rounded"
                                controls={true}
                            />
                        </div>
                    )}
                    {personalVideoPreviewUrl && personalVideo && (
                        <div className="mt-4">
                            <h5 className="font-semibold">Face Video</h5>
                            <p>File name: {personalVideo.name}</p>
                            {personalVideoDuration && <p>Duration: {personalVideoDuration}</p>}
                            <video
                                src={personalVideoPreviewUrl}
                                style={{height: '280px', width: '100%', objectFit: 'contain'}}
                                className="mt-2 rounded"
                                controls={true}
                            />
                        </div>
                    )}

                    {/* Display recorded video */}
                    {recordedVideo && (
                        <div className="mt-3">
                            <video
                                src={URL.createObjectURL(recordedVideo)}
                                controls
                                className="w-full mt-2"
                            />
                        </div>
                    )}
                    {showCamera && (
                        <div className="mt-6 p-6 bg-[#F0EBF8] rounded-xl">
                            <Cameras onVideoCapture={handleVideoCapture}/>
                        </div>
                    )}

                    {personalVideo && personalVideoDuration && (
                        (() => {
                            // Parse the duration string correctly
                            let durationInSeconds = 0;
                            if (personalVideoDuration.includes(':')) {
                                const [minutes, seconds] = personalVideoDuration.split(':').map(Number);
                                durationInSeconds = minutes * 60 + seconds;
                            } else {
                                durationInSeconds = parseFloat(personalVideoDuration);
                            }
                            return durationInSeconds < MIN_DURATION || durationInSeconds > MAX_DURATION;
                        })()
                    ) && (
                        <p className="text-red-500 mt-2">
                            The video should be between {MIN_DURATION} and {MAX_DURATION} seconds long
                        </p>
                    )}

                </div>

                {/* YouTube Video Input */}
                <div className="p-6 bg-[#F0EBF8] shadow-xl rounded-xl">
                    <div className="flex items-center gap-2 text-[#673AB7] bg-transparent ">
                        <h5 className="mx-auto text-lg font-semibold -mt-2 -mb-1">Reference Video</h5>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <InfoIcon className="w-5 h-5 hover:cursor-pointer hover:text-[#F4804D]"/>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p className="max-w-xs">Paste URL of a video you want to copy content from. Videos
                                        with variety of different scenes are recommended!</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Separator className="my-4 bg-gray-300 "/>
                    <Input
                        type="url"
                        placeholder="Drop a YouTube URL here"
                        value={youtubeURL}
                        startIcon={LinkIcon}
                        onChange={(e) => setYoutubeURL(e.target.value)}
                        onBlur={(e) => {
                            if (e.target.value && !validateYouTubeUrl(e.target.value)) {
                                setError(true);
                                setErrorMessage('Please enter a valid YouTube URL.');
                                e.target.classList.add('border-red-500');
                            } else {
                                e.target.classList.remove('border-red-500');
                            }
                        }}
                        className="bg-white py-5 mt-1 border-gray-400 rounded-sm"
                    />

                    {/*Empty Div for padding */}
                    <div className={"pt-3"}/>

                    <SampleVideosModal videoType={"reference"} onSelectVideo={HandleReferenceVideoSelect}/>

                    {youtubeURL && !validateYouTubeUrl(youtubeURL) ? (
                        <div className="mt-4 text-red-500 p-4 rounded-lg">
                            YouTube URL is not valid. Please enter a valid YouTube URL.
                        </div>
                    ) : (youtubeURL && youTubeVideoInfo) && (
                        <div className="mt-4">
                            <h5 className="font-semibold">YouTube Video</h5>
                            <p>Title: {youTubeVideoInfo.title}</p>
                            <p>Duration: {youTubeVideoInfo.duration}</p>
                            <iframe
                                src={`https://www.youtube.com/embed/${extractYouTubeVideoId(youtubeURL)}`}
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowFullScreen
                                style={{height: '280px', width: '100%', objectFit: 'contain'}}
                                className="mt-2 rounded"
                            />
                            {youTubeVideoInfo.exceeds_limit && (
                                <p className="text-red-500 mt-2">
                                    The video should be between 5 and 20 minutes long
                                </p>
                            )}
                        </div>
                    )}

                </div>
            </div>
            {(error || jobStatusError) && (
                <div className="bg-red-100 text-red-500 p-4 mt-4 rounded-lg">
                    {errorMessage || jobStatusError?.message}
                </div>
            )}

            <div
                className="p-4 flex flex-col sm:flex-row  items-center mx-auto max-w-6xl justify-center mt-1 gap-4">
                {/*ghost div for padding */}
                <div className="flex items-center w-full sm:w-auto sm:px-16 mb-3 mx-4 sm:mb-0"/>
                <Button
                    variant="outline"
                    className="text-center px-7 py-5 rounded-full text-base font-semibold text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:text-white hover:cursor-pointer"
                    onClick={handleFuseButtonCLick}
                    disabled={error}
                >
                    {isPending || isProcessingModalOpen ? (
                        <div className="flex items-center justify-center gap-2">
                            <ClipLoader size={18} color="#ffffff"/>
                            <span>Processing...</span>
                        </div>
                    ) : <span className={"flex items-center justify-center gap-2"}>

                        Fuse Videos
                        <SparklesIcon/>
                    </span>}
                </Button>
                <div className="flex items-center w-full sm:w-auto justify-between sm:justify-start gap-4 sm:gap-2">
                    <Language onLangChange={handleLanguageSelect} currentLang={selectedLanguage}/>
                    <div className="px-3 text-[#673AB7] bg-transparent">
                        <Tooltip>
                            <TooltipTrigger>
                                <InfoIcon className="w-5 h-5 hover:cursor-pointer hover:text-[#F4804D]"/>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="max-w-xs">Please select the desired language for your final video. Content
                                    from your reference video will be translated to the selected language.</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>
            </div>
            {
                isProcessingModalOpen && (
                    <div className="flex flex-col items-center justify-center mx-auto space-y-6 py-4">
                        <div ref={progressRef} className="w-full max-w-xs">
                            <Progress
                                value={jobStatusData?.status === JobStatus.COMPLETED ? 100 : progressPercentage}
                                className="h-2 [&>*]:bg-gradient-to-r [&>*]:from-purple-800 [&>*]:to-purple-600"
                            />
                        </div>
                        <div className="text-sm text-gray-500">
                            {processingMessages[currentMessageIndex]} {Math.round(progressPercentage)}%
                        </div>
                    </div>
                )
            }


            {jobStatusData?.status === JobStatus.COMPLETED && (
                <div className="mt-4">
                    <div className="bg-[#d1e7dd] text-[#0f5132] mt-4 border border-[#badbcc]
                    p-4 flex flex-col sm:flex-row  items-center mx-auto max-w-7xl justify-center mt-5 gap-4 shadow-xl rounded-xl
                    ">
                        Videos fused successfully! The fused video is ready to play.
                    </div>
                    <Separator/>
                    {jobStatusData.downloadUrl && (
                        <>
                            <Card className="mx-auto max-w-4xl mt-10 bg-[#F0EBF8] shadow-xl">
                                <CardHeader>
                                    <CardTitle className="text-3xl font-bold text-[#F4804E] text-center -pb-2 -pt-2">Fused
                                        Video</CardTitle>
                                </CardHeader>
                                <CardContent className="p-0">
                                    <video
                                        ref={videoRef}
                                        src={jobStatusData.downloadUrl}
                                        controlsList="nodownload"
                                        controls
                                        className="w-full rounded-lg max-h-[70vh] object-contain"
                                    />
                                </CardContent>
                                <CardFooter
                                    className="flex flex-col sm:flex-row items-center justify-between w-full">
                                    <div className="flex items-center space-x-2">
                                        <h5>
                                            Note: To download the video in high resolution, with flawless lip
                                            movement and subtitles in 30+ languages, please {" "}
                                            <Link to="/signup"
                                                  className="text-[#E46F3E] font-semibold hover:underline  cursor-pointer">
                                                sign-up
                                            </Link>
                                            {" "}to get access to the full product
                                        </h5>
                                    </div>
                                </CardFooter>
                            </Card>
                        </>
                    )}
                </div>
            )}

            <NotEnoughCreditsModal
                isOpen={showNotEnoughCreditsDialog}
                onOpenChange={(open) => {
                    if (!open) {
                        resetNotEnoughCreditsDialog();
                    }
                }}
            />
            <div className="mt-50"/>
        </div>
    );
}

