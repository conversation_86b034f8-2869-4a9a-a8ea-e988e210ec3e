import {Instagram, Linkedin, Twitter, Youtube} from "lucide-react";
import {navLinks, socialLinks} from "@/lib/constants.ts";
import {Link, useLocation} from "@tanstack/react-router";
import {useEffect, useState} from "react";
import {useAuth} from "@/auth/use-auth.ts";

const socialIconMap: Record<string, React.ReactNode> = {
    Twitter: <Twitter className="h-6 w-6"/>,
    LinkedIn: <Linkedin className="h-6 w-6"/>,
    YouTube: <Youtube className="h-6 w-6"/>,
    Instagram: <Instagram className="h-6 w-6"/>,
};

export default function Footer() {

    const {user} = useAuth();
    const location = useLocation();
    const [currentPath, setCurrentPath] = useState("/");

    useEffect(() => {
        setCurrentPath(location.pathname);
    }, [location]);
    const isAuthenticated = !!user && !user.isAnonymous;

    return (
        <footer className={`${currentPath === "/" ? "bg-[#ca9de9]" : "bg-[#F0EBF8]"} text-gray-800`}>
            <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Logo and Social Links */}
                    <div className="lg:col-span-2">
                        <Link to="/" className="font-bold text-2xl flex items-center gap-3">
                            <svg width="50" height="32" viewBox="0 0 50 40" fill="none">
                                {/* Back circles (clone) */}
                                <circle cx="30" cy="20" r="20" fill="#F4804E" opacity="0.5"/>
                                <path d="M33 20L21 27V13L33 20Z" fill="white" opacity="0.5"/>
                                <circle cx="25" cy="20" r="20" fill="#F4804E" opacity="0.4"/>
                                <path d="M33 20L21 27V13L33 20Z" fill="white" opacity="0.4"/>

                                {/* Front circle (original) */}
                                <circle cx="35" cy="20" r="20" fill="#F4804E"/>
                                <path d="M43 20L31 27V13L43 20Z" fill="white"/>
                            </svg>
                            <span
                                className="text-2xl bg-clip-text text-transparent bg-gradient-to-r from-purple-700 to-purple-900">Clones
                            </span>
                        </Link>
                        <p className="mt-3 text-purple-900 max-w-xs text-sm">
                            The future of video creation is here. No camera, no crew, no complicated editing.
                        </p>
                        <div className="mt-4 flex space-x-4">
                            {socialLinks.map((social, index) => (
                                <a
                                    key={index}
                                    href={social.href}
                                    className="text-purple-800 hover:text-purple-900 transition-colors"
                                    aria-label={social.name}
                                >
                                    {socialIconMap[social.name]}
                                </a>
                            ))}
                        </div>
                    </div>

                    {/* Main Navigation Links */}
                    <div className="lg:col-span-2 flex flex-col md:flex-row md:justify-end gap-6">
                        {navLinks.map((link, index) => {
                            const shouldShow = link.to !== currentPath &&
                                (link.showForAuthenticated === null ||
                                 (isAuthenticated && link.showForAuthenticated === true) ||
                                 (!isAuthenticated && link.showForAuthenticated === false));
                            return shouldShow && (
                                <div key={index}>
                                    <Link
                                        to={link.to}
                                        className="text-md font-semibold text-purple-900">{link.name}</Link>
                                </div>
                            );
                        })}
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                    {/* Left side - empty to balance layout */}
                    <div className="lg:col-span-2"></div>

                    {/* Right side - Google Translate */}
                    {/*<div className="lg:col-span-2 flex flex-col items-end">*/}
                    {/*    <div className="w-full md:w-64">*/}
                    {/*        <GoogleTranslate />*/}
                    {/*    </div>*/}
                    {/*</div>*/}
                </div>

                <div
                    className="mt-6 pt-6 border-t border-purple-100 flex flex-col md:flex-row justify-between items-center">
                    <p className="text-purple-900 text-sm">© 2025 Clones. All rights reserved.</p>
                </div>
            </div>
        </footer>
    );
}