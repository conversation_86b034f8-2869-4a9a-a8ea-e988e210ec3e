import React from 'react';
import { Card } from '@/components/ui/card';

interface GradientBorderCardProps extends React.ComponentProps<typeof Card> {
  isCurrentPlan?: boolean;
  isPopular?: boolean;
}

export function GradientBorderCard({ 
  children, 
  className = '', 
  isCurrentPlan = false,
  isPopular = false,
  ...props 
}: GradientBorderCardProps) {
  return (
    <div className={`
      relative rounded-xl p-[2px] overflow-hidden
      ${isCurrentPlan ? 'ring-offset-2 ring-2 ring-transparent' : ''}
      ${isPopular ? 'shadow-lg' : ''}
      bg-gradient-to-r from-purple-700 to-orange-400
    `}>
      <Card 
        className={`
          rounded-[10px] h-full
          ${className}
        `} 
        {...props}
      >
        {children}
      </Card>
    </div>
  );
}
