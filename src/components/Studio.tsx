import {Button} from "@/components/ui/button.tsx";
import {Input} from "@/components/ui/input.tsx";
import {useEffect, useRef, useState, useMemo} from "react";
import Cameras from "@/components/Camera.tsx";
import SampleVideosModal, {formatDuration} from "@/components/SampleVideosModal.tsx";
import {VideoReturnDTO} from "@/utils/hooks/video/useSampleVideos";
import {useYoutubeInfo, YoutubeVideoInfo} from "@/utils/hooks/video/useYoutubeInfo.ts";
import {usePresignedUrl} from "@/utils/hooks/video/usePresignedUrl.ts";
import {MAX_DURATION, MIN_DURATION} from "@/utils/constants.ts";
import {GetUploadUrlResponse, useGetUploadUrl} from "@/utils/hooks/video/useUploadUrl.ts";
import {uploadImageToS3, uploadVideoToS3} from "@/utils/hooks/video/uploadVideoToS3.ts";
import {useProcessVideo} from "@/utils/hooks/video/useProcessVideo.ts";
import {JobStatus, useCheckJobStatus} from "@/utils/hooks/video/useVideoProcessing.ts";
import {triggerEvent} from "@/components/PageTracking.tsx";
import Language from "@/components/Language.tsx";
import NotEnoughCreditsModal from "@/components/NotEnoughCreditsModal.tsx";
import UserFuseVideos from "@/components/UserFuseVideos.tsx";
import {Download, InfoIcon, Link, Loader, Plus, RefreshCw, AlertCircle} from "lucide-react";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogFooter,
    DialogTitle,
    DialogTrigger,
    DialogDescription
} from "@/components/ui/dialog.tsx";
import MagicScriptModal from "@/components/MagicScriptModal.tsx";
import RecommendedVideoTypes from "@/components/RecommendedVideoTypes.tsx";
import {Separator} from "@/components/ui/separator.tsx";
import {ClipLoader} from "react-spinners";
import {Progress} from "@/components/ui/progress.tsx";
import {Label} from "@/components/ui/label.tsx";
import {SparklesIcon} from "@/components/ui/SparklesIcon.tsx";
import ResponsiveFusedVideoLengthDropdown from "@/components/FusedVideoLengthDropdown.tsx";
import {Card, CardContent, CardFooter, CardHeader, CardTitle} from "@/components/ui/card.tsx";
import {useLipsync} from "@/utils/hooks/video/useLipsync.ts";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip.tsx";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu.tsx";
import {useDownload1080p} from "@/utils/hooks/video/useDownload1080p.ts";
import {useRetryVideo} from "@/utils/hooks/video/useRetryVideo.ts";
import {useUserWithSubscription} from "@/utils/hooks/user/useUser.ts";
import {getVideoDuration, getVideoOrientation, getVideoThumbnail} from "@/lib/video.ts";

export default function Studio() {
    const [personalVideo, setPersonalVideo] = useState<File | null>(null);
    const [personalVideoPreviewUrl, setPersonalVideoPreviewUrl] = useState<string | null>(null);
    const [personalVideoDuration, setPersonalVideoDuration] = useState<string>("");
    const [error, setError] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [youtubeURL, setYoutubeURL] = useState("");
    const [recordedVideo, setRecordedVideo] = useState<Blob | null>(null);
    const [showCamera, setShowCamera] = useState(false);
    const [youTubeVideoInfo, setYouTubeVideoInfo] = useState<YoutubeVideoInfo | undefined>({
        video_id: '',
        title: '',
        thumbnail: '',
        duration: '',
        exceeds_limit: false,
    });
    const [s3Key, setS3Key] = useState<string>('');
    const [processedVideoID, setProcessedVideoID] = useState("");
    const [isProcessing, setIsProcessing] = useState(false);
    const [fuseVideoLength, setFuseVideoLength] = useState(90);
    const [personalSampleVideoSelected, setPersonalSampleVideosSelected] = useState<VideoReturnDTO | null>(null);
    const [isProcessingModalOpen, setIsProcessingModalOpen] = useState(!!processedVideoID);
    const [selectedLanguage, setSelectedLanguage] = useState(localStorage.getItem('language') || 'en');
    const [isLipSyncModalOpen, setIsLipSyncModalOpen] = useState(false);
    const [selectedLipSyncUrl, setSelectedLipSyncUrl] = useState<string | null>(null);
    const [selectedLipSyncThumbnail, setSelectedLipSyncThumbnail] = useState<string>("");
    const [progressPercentage, setProgressPercentage] = useState(0);
    const [retryProcessingVideoIds, setRetryProcessingVideoIds] = useState<string[]>([]);
    const [processing1080pVideoIds, setProcessing1080pVideoIds] = useState<string[]>([]);
    const [confirmRetryModalOpen, setConfirmRetryModalOpen] = useState(false);
    const [confirmLipSyncModalOpen, setConfirmLipSyncModalOpen] = useState(false);
    const [processingMessages] = useState([
        "Please wait while your video is being prepared... good things take time!",
        "This can take about 10–15 minutes depending on video duration and language complexity.",
        "You're welcome to stick around, or feel free to close this tab — we'll email you as soon as it's ready.",
        "Analyzing your video to capture your unique voice and style.",
        "Once it's ready, you can use your video right away or fine-tune the lip sync for an even better result.",
        "Analyzing your video to capture your unique voice and style.",
        "Fun fact: The first-ever video uploaded to YouTube was about an elephant! 🐘",
        "Scanning the reference video—extracting the best bits like an AI detective.",
        "You're welcome to stick around, or feel free to close this tab — we'll email you as soon as it's ready.",
        "Scanning the reference video—extracting the best bits like an AI detective.",
        "Pro tip: Good things take time, great things take AI! 😉",
        "Summarizing the topic and crafting a fresh, engaging script.",
        "Once it's ready, you can use your video right away or fine-tune the lip sync for an even better result.",
        "Now cloning you in the video—don't worry, no evil twin involved.",
        "You're welcome to stick around, or feel free to close this tab — we'll email you as soon as it's ready.",
        "Adding subtitles—because who doesn't love karaoke mode?",
        "If you stare at this progress bar long enough, it might just move faster.",
        "Final touches—this is where the AI sprinkles its fairy dust.",
        "Just a little longer… Rome wasn't built in a day, but your video will be done in minutes!",
        "You're welcome to stick around, or feel free to close this tab — we'll email you as soon as it's ready.",
        "AI is working its magic—meanwhile, maybe do some stretches?",
        "Your video is almost ready... but can AI make popcorn too?",
        "Once it's ready, you can use your video right away or fine-tune the lip sync for an even better result.",
        "This is the part where AI flexes its muscles. Processing…",
        "Hold tight! We're making sure this video is worth the wait."
    ]);
    const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
    const videoRef = useRef<HTMLVideoElement>(null);
    const progressRef = useRef<HTMLDivElement>(null);
    const progressTimerRef = useRef<NodeJS.Timeout | null>(null);
    const messageRotationTimerRef = useRef<NodeJS.Timeout | null>(null);
    const [isAddingLipsync, setIsAddingLipsync] = useState(false);
    const isDisabled = () => isProcessingModalOpen;

    const {data: jobStatusData, error: jobStatusError} = useCheckJobStatus(processedVideoID);
    const {mutate: addLipsync} = useLipsync();
    const {mutate: retryVideo} = useRetryVideo();
    const {mutateAsync: download1080p} = useDownload1080p();
    const {data: userData} = useUserWithSubscription();

    // Determine max retry attempts based on subscription
    const maxRetryAttempts = useMemo(() => {
        const subscription = userData?.subscription;
        const isFreePlan = !subscription || !subscription.status || subscription.status !== "active" || subscription.productName.includes("Free");
        return isFreePlan ? 1 : 3;
    }, [userData]);

    useEffect(() => {
        const fetchData = async () => {
            if (s3Key && youtubeURL)
                await processVideo(s3Key, youtubeURL, selectedLanguage);
        };
        void fetchData()

    }, [s3Key, youtubeURL, selectedLanguage, fuseVideoLength]);

    // Progress timer effect
    useEffect(() => {
        // Start the progress timer when the processing modal is opened
        if (isProcessingModalOpen) {
            // Reset progress percentage
            setProgressPercentage(0);
            // Reset message index
            setCurrentMessageIndex(0);

            // Clear any existing timers
            if (progressTimerRef.current) {
                clearInterval(progressTimerRef.current);
            }
            if (messageRotationTimerRef.current) {
                clearInterval(messageRotationTimerRef.current);
            }

            // Calculate the increment needed to reach 100% in 10 minutes
            // Update every 3 seconds, so we need 200 updates to reach 100% in 10 minutes
            const totalUpdates = 200;
            const incrementPerUpdate = 100 / totalUpdates;

            // Start the progress timer
            progressTimerRef.current = setInterval(() => {
                setProgressPercentage(prev => {
                    // If job is already completed, set to 100%
                    if (jobStatusData?.status === JobStatus.COMPLETED) {
                        return 100;
                    }

                    // Cap at 99% until we get confirmation of completion
                    const newValue = prev + incrementPerUpdate;
                    return newValue >= 99 ? 99 : newValue;
                });
            }, 3000); // Update every 3 seconds

            // Start the message rotation timer
            messageRotationTimerRef.current = setInterval(() => {
                setCurrentMessageIndex(prevIndex => {
                    // If we're at the last message, stay there
                    if (prevIndex === processingMessages.length - 1) {
                        return prevIndex;
                    }
                    return prevIndex + 1;
                });
            }, 15000);
            return () => {
                // Clean up both timers
                if (progressTimerRef.current) {
                    clearInterval(progressTimerRef.current);
                    progressTimerRef.current = null;
                }
                if (messageRotationTimerRef.current) {
                    clearInterval(messageRotationTimerRef.current);
                    messageRotationTimerRef.current = null;
                }
            };
        }
    }, [isProcessingModalOpen, jobStatusData?.status, processingMessages.length]);


    // Auto-scroll to progress bar or video based on job status
    useEffect(() => {
        if (jobStatusData?.status === JobStatus.COMPLETED && jobStatusData.downloadUrl && videoRef.current) {
            // Scroll to video when processing is complete
            videoRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        } else if ((jobStatusData?.status === JobStatus.PENDING || jobStatusData?.status === JobStatus.PROCESSING) && progressRef.current) {
            // Scroll to progress bar when processing is in progress
            progressRef.current.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }, [jobStatusData?.status, jobStatusData?.downloadUrl]);


    // remove error message on change of personal video or youtube url or personal sample video selected
    useEffect(() => {
        setError(false);
        setErrorMessage(null);
    }, [personalVideo, youtubeURL, personalSampleVideoSelected]);

    // Cleanup object URL when component unmounts or when video changes
    useEffect(() => {
        return () => {
            if (personalVideoPreviewUrl) {
                URL.revokeObjectURL(personalVideoPreviewUrl);
            }
        };
    }, [personalVideoPreviewUrl]);

    //  open processing modal when the video is being processed
    useEffect(() => {
        if (jobStatusData?.status === JobStatus.COMPLETED) {
            setIsProcessingModalOpen(false);
            // Clear all timers
            if (progressTimerRef.current) {
                clearInterval(progressTimerRef.current);
                progressTimerRef.current = null;
            }
            if (messageRotationTimerRef.current) {
                clearInterval(messageRotationTimerRef.current);
                messageRotationTimerRef.current = null;
            }
        } else if (jobStatusData?.status === "FAILED" || jobStatusError) {
            setIsProcessingModalOpen(false);
            setError(true);
            setErrorMessage("Failed to process video, Please try again");
            // Clear all timers
            if (progressTimerRef.current) {
                clearInterval(progressTimerRef.current);
                progressTimerRef.current = null;
            }
            if (messageRotationTimerRef.current) {
                clearInterval(messageRotationTimerRef.current);
                messageRotationTimerRef.current = null;
            }
        }
    }, [jobStatusData, processedVideoID, jobStatusError]);

    // Function to handle video recording completion
    const handleVideoCapture = (videoBlob: Blob) => {
        // Clear other video sources when recording a video
        setPersonalVideo(null);
        setPersonalVideoPreviewUrl(null);
        setPersonalVideoDuration("");
        setPersonalSampleVideosSelected(null);

        setRecordedVideo(videoBlob);
        setShowCamera(false);
        triggerEvent({eventName: 'video_recorded', page_section: 'body'})
    };

    const {data: YoutubeVideoInfo} = useYoutubeInfo(youtubeURL);
    useEffect(() => {
        if (YoutubeVideoInfo) {
            setYouTubeVideoInfo(YoutubeVideoInfo);
        }
    }, [YoutubeVideoInfo]);

    const HandleReferenceVideoSelect = (video: VideoReturnDTO) => {
        const youtubeURL = `https://www.youtube.com/watch?v=${video.video_id}`;
        setYoutubeURL(youtubeURL);
        triggerEvent({eventName: 'clicked_reference_video', page_section: 'body'})

    }

    const filterSampleVideoPreviewPresignedUrl = (video: VideoReturnDTO | null): string => {
        if (video) {
            const sampleVideoName = `${video.video_id}.${video.title.split('.').pop()}`
            return `user-uploads/${sampleVideoName}`
        }
        return "";
    }

    const {
        data: personalSampleVideoPresigned,
    } = usePresignedUrl(filterSampleVideoPreviewPresignedUrl(personalSampleVideoSelected));


    const HandlePersonalVideoSelect = (video: VideoReturnDTO) => {
        // Clear other video sources when selecting a sample video
        setPersonalVideo(null);
        setPersonalVideoPreviewUrl(null);
        setPersonalVideoDuration("");
        setRecordedVideo(null);
        setShowCamera(false);
        // Set the selected sample video
        setPersonalSampleVideosSelected(video);
        triggerEvent({eventName: 'clicked_personal_video', page_section: 'body'})
    }


    const {mutate: fetchUploadUrl} = useGetUploadUrl();

    const handleFuseButtonCLick = async () => {
        setS3Key("") // reset s3 key
        triggerEvent({eventName: 'clicked_fuse_videos', page_section: 'body'})
        try {
            if (!recordedVideo && !personalVideo && !personalSampleVideoSelected) {
                console.log("Error")
                setErrorMessage("Please upload or record a personal video");
                setError(true);
                return;
            }

            if (!youtubeURL) {
                setErrorMessage('Please enter a YouTube URL.');
                setError(true)
                return;
            }

            if (youTubeVideoInfo && youTubeVideoInfo.exceeds_limit) {
                setErrorMessage('Please post url of a YouTube video that is between 5 and 20 minutes long');
                setError(true);
                return;
            }
            if (personalVideo) {
                const duration = await getVideoDuration(personalVideo);
                const orientation = await getVideoOrientation(personalVideo);
                if (duration < MIN_DURATION || duration > MAX_DURATION) {
                    setErrorMessage(`Please upload a face video that is between ${MIN_DURATION} and ${MAX_DURATION} seconds long.`);
                    setError(true);
                    return;
                }
                if (orientation === 'portrait') {
                    setErrorMessage('Please upload a square or landscape video. Portrait is not supported.');
                    setError(true)
                    return;
                }
                const thumbnail = await getVideoThumbnail(personalVideo);
                try {
                    setIsProcessing(true);
                    fetchUploadUrl({
                        fileName: personalVideo.name.replace(/ /g, '-'),
                        fileType: personalVideo.type,
                        duration: String(duration),
                    }, {
                        onSuccess: async (data: GetUploadUrlResponse) => {
                            if (data.uploadUrl !== "already_uploaded") {
                                await uploadVideoToS3(personalVideo, data.uploadUrl);
                                await uploadImageToS3(thumbnail, personalVideo.name, String(duration));
                            }
                            setS3Key(data.s3Key)
                        },
                        onError: (error) => {
                            console.log(error);
                        }
                    });
                } catch {
                    setErrorMessage('Failed to get upload URL.');
                    setError(true);
                    return;
                }
            } else if (personalSampleVideoSelected) {
                setS3Key("user-uploads/" + personalSampleVideoSelected?.video_id);
            } else if (recordedVideo) {
                //     convert blob into file and upload to s3 and get the s3 key
                const file = new File([recordedVideo], 'PV-' + Math.floor(10000 + Math.random() * 90000) + ".webm", {type: 'video/webm'});
                const duration = await getVideoDuration(file);
                const orientation = await getVideoOrientation(file);
                if (duration < MIN_DURATION || duration > MAX_DURATION) {
                    setErrorMessage(`Please upload a face video that is between ${MIN_DURATION} and ${MAX_DURATION} seconds long.`);
                    setError(true);
                    triggerEvent({eventName: 'fuse_video_duration_error', page_section: 'body'})
                    return;
                }
                if (orientation === 'portrait') {
                    setErrorMessage('Please upload a square or landscape video. Portrait is not supported.');
                    setError(true)
                    triggerEvent({eventName: 'fuse_video_orientation_error', page_section: 'body'})
                    return;
                }
                const thumbnail = await getVideoThumbnail(file);
                try {
                    fetchUploadUrl({
                        fileName: file.name.replace(/ /g, '-'),
                        fileType: file.type,
                        duration: String(duration),
                    }, {
                        onSuccess: async (data: GetUploadUrlResponse) => {
                            if (data.uploadUrl !== "already_uploaded") {
                                await uploadVideoToS3(file, data.uploadUrl);
                                await uploadImageToS3(thumbnail, file.name, String(duration));
                            }
                            setS3Key(data.s3Key)
                        },
                        onError: (error) => {
                            console.log(error);
                            setErrorMessage('Failed to get upload URL.');
                            setError(true);
                            triggerEvent({eventName: 'fuse_video_upload_error', page_section: 'body'})
                        }
                    });
                } catch {
                    setErrorMessage('Failed to get upload URL.');
                    setError(true);
                    triggerEvent({eventName: 'fuse_video_upload_error', page_section: 'body'})

                }
            }
        } catch {
            console.log("Error")
        }
    };

    const {
        mutateAsync,
        isPending,
        isError: processingVideoError,
        showNotEnoughCreditsDialog,
        resetNotEnoughCreditsDialog,
    } = useProcessVideo();

    useEffect(() => {
        if (isPending) {
            setIsProcessing(true);
        } else {
            setIsProcessing(false);
        }
    }, [isPending]);

    const processVideo = async (s3Key: string, youtubeUrl: string, language: string) => {
        if (isPending) {
            return;
        }
        if (processingVideoError) {
            setErrorMessage('Processing video error');
            setError(true);
            return;
        }

        try {
            mutateAsync({
                personal_video_s3_key: s3Key,
                youtube_url: youtubeUrl,
                language: language,
                length: fuseVideoLength
            }).then(
                (data) => {
                    setProcessedVideoID(data.video_id);
                    setIsProcessingModalOpen(true);
                    triggerEvent({eventName: 'fuse_video_success', page_section: 'body'})
                }
            )
        } catch {
            setErrorMessage('Failed to process video');
            setError(true);
        }

    }

    const extractYouTubeVideoId = (url: string) => {
        const match = url.match(/(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([^&]+)/);
        return match ? match[1] : '';
    }

    const validateYouTubeUrl = (url: string) => {
        const pattern = /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/|v\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(\S+)?$/;
        return pattern.test(url);
    };

    const handleLanguageSelect = (lang: string) => {
        setSelectedLanguage(lang);
        triggerEvent({eventName: 'language_selected', page_section: 'body'})
    }

    const handleAddLipSync = () => {
        if (processedVideoID) {
            addLipsync({
                video_id: processedVideoID
            });
            setIsAddingLipsync(true);
            setConfirmLipSyncModalOpen(false);

            triggerEvent({eventName: 'add_lipsync', page_section: 'body'})
        }
    };

    const openConfirmLipSyncModal = () => {
        if (processedVideoID) {
            setConfirmLipSyncModalOpen(true);
        }
    };

    const openLipSyncModal = (lipsyncUrl: string, thumbnail: string) => {
        if (!lipsyncUrl) {
            return;
        }
        setSelectedLipSyncUrl(lipsyncUrl);
        setSelectedLipSyncThumbnail(thumbnail);
        setIsLipSyncModalOpen(true);
        triggerEvent({eventName: 'view_lipsync', page_section: 'body'})
    };

    const handleDownloadWithResolution = async (resolution: '720p' | '1080p', lipsynced: boolean = false) => {
        let url = "";

        try {
            if (lipsynced && jobStatusData?.lipsync_s3_key) {
                url = jobStatusData.lipsync_s3_key;
            } else {
                if (resolution === '720p') {
                    url = jobStatusData?.downloadUrl || "";
                }
                if (resolution === '1080p') {
                    if (jobStatusData?.download_1080p_s3_key === "PROCESSING" || processing1080pVideoIds.includes(processedVideoID)) {
                        setErrorMessage('Video is currently being processed for 1080p');
                        setError(true);
                        return;
                    } else if (!jobStatusData?.download_1080p_s3_key || jobStatusData?.download_1080p_s3_key === "") {
                        if (processedVideoID) {
                            setProcessing1080pVideoIds(prev => [...prev, processedVideoID]);
                            await download1080p({video_id: processedVideoID}).then((data) => {
                                if (data.status === 'error') {
                                    setErrorMessage(data.message || 'Failed to download 1080p');
                                    setError(true);
                                    setProcessing1080pVideoIds(prev => prev.filter(id => id !== processedVideoID));
                                }
                            });
                        }
                        return;
                    } else {
                        url = jobStatusData.download_1080p_s3_key;
                    }
                }
            }

            if (url) {
                // Check if the environment is iOS or Safari
                const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
                const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

                if (isIOS || isSafari) {
                    // For iOS/Safari, create an iframe to trigger download
                    const iframe = document.createElement('iframe');
                    iframe.style.display = 'none';
                    document.body.appendChild(iframe);

                    // Set iframe source to the URL
                    iframe.src = url;

                    // Remove the iframe after a delay
                    setTimeout(() => {
                        document.body.removeChild(iframe);

                        // If iframe approach doesn't trigger download, fallback to direct navigation
                        // Show a message to the user
                        const downloadStarted = confirm('If your download didn\'t start automatically, click OK to open the video in a new tab where you can save it manually.');

                        if (downloadStarted) {
                            window.open(url, '_blank');
                        }
                    }, 2000);
                } else {
                    // For other devices, use the anchor tag for download
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `fused-video-${resolution}${lipsynced ? '-lipsync' : ''}.mp4`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
            }
        } catch (error) {
            setErrorMessage('Failed to download video');
            setError(true);
        }
    };
    const openConfirmRetryModal = () => {
        if (processedVideoID) {
            setConfirmRetryModalOpen(true);
        }
    };

    const handleRetryVideo = () => {
        if (processedVideoID) {
            setRetryProcessingVideoIds(prev => [...prev, processedVideoID]);
            retryVideo({video_id: processedVideoID});
            setConfirmRetryModalOpen(false);
            triggerEvent({eventName: 'retry_video_processing', page_section: 'body'})
        }
    };

    return (
        <div className="container mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 max-w-7xl mx-auto gap-4">
                {/* Personal Video Upload */}
                <div className="p-6 bg-[#F0EBF8] shadow-xl rounded-xl ">
                    <div className="flex items-center gap-2 text-[#673AB7] bg-transparent ">
                        <h5 className=" mx-auto text-lg font-semibold -mt-2 -mb-1 ">Face Video</h5>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <InfoIcon className="w-5 h-5 hover:cursor-pointer hover:text-[#F4804D]"/>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p className="max-w-xs">Use a sample face video or record your face video
                                        <br/>
                                        * Record a 1-min video of yourself
                                        <br/>
                                        * Keep camera horizontal (landscape mode)
                                        <br/>
                                        * Use the magic script or use your own words
                                    </p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Separator className="my-4 bg-gray-300 "/>
                    <p className="text-gray-600 mt-3 mb-3">
                        Select a face from our samples or upload a quick 1-minute video of yourself reading this {" "}
                        <Dialog>
                            <DialogTrigger className="text-[#E46F3E] hover:underline cursor-pointer font-semibold">
                                magic script
                            </DialogTrigger>
                            <DialogContent title="Magic Script" className={"min-w-1/2"}>
                                <DialogTitle>Magic Script</DialogTitle>
                                <MagicScriptModal/>
                                <DialogFooter>
                                    <DialogClose asChild>
                                        <Button
                                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                            aria-label="Close"
                                        >
                                            Close
                                        </Button>
                                    </DialogClose>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>
                        {" "}to create your AI twin.
                    </p>
                    <div className="flex bg-white py-1 items-center rounded-sm gap-1 px-2 border border-gray-400">
                        <Label className="relative cursor-pointer">
                            <span
                                className="bg-gray-200 text-gray-700 py-2 px-4 rounded-sm inline-flex items-center justify-center w-[100px] h-9 whitespace-nowrap">
                                Choose File
                            </span>
                            <Input
                                type="file"
                                accept="video/*"
                                onChange={async (e) => {
                                    const file = e.target.files ? e.target.files[0] : null;
                                    // Clear other video sources when uploading a personal video
                                    setPersonalSampleVideosSelected(null);
                                    setRecordedVideo(null);
                                    setPersonalVideo(file);
                                    setShowCamera(false);
                                    // Create preview URL for the uploaded video
                                    if (file) {
                                        const url = URL.createObjectURL(file);
                                        setPersonalVideoPreviewUrl(url);
                                        // Calculate and set the duration
                                        try {
                                            const durationInSeconds = await getVideoDuration(file);
                                            setPersonalVideoDuration(formatDuration(String(durationInSeconds)));
                                        } catch (error) {
                                            console.error("Error calculating video duration:", error);
                                            setPersonalVideoDuration("");
                                        }
                                    } else {
                                        setPersonalVideoPreviewUrl(null);
                                        setPersonalVideoDuration("");
                                    }
                                }}
                                className={`absolute inset-0 w-full h-full opacity-0 cursor-pointer ${isDisabled() ? 'hidden' : ''}`}
                                disabled={isDisabled()}
                            />
                        </Label>
                        <p className="truncate text-gray-500 w-full ml-1">
                            {personalVideo?.name || "No file chosen"}
                        </p>
                    </div>

                    <div className="flex flex-wrap gap-2 mt-3">
                        <Button
                            variant="outline"
                            onClick={() => {
                                setPersonalVideo(null);
                                setPersonalVideoPreviewUrl(null);
                                setPersonalVideoDuration("");
                                setPersonalSampleVideosSelected(null);
                                setShowCamera(true)
                            }
                            }
                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                            disabled={isDisabled()}
                        >
                            Record Video
                        </Button>
                        <SampleVideosModal videoType={"personal"} onSelectVideo={HandlePersonalVideoSelect} disabled={isDisabled()}/>
                    </div>

                    {personalSampleVideoPresigned && (
                        <div className="mt-4">
                            <h5 className="font-semibold">Sample Face Video</h5>
                            <p>Title: {personalSampleVideoSelected?.title}</p>
                            <p>Duration: {formatDuration(personalSampleVideoSelected?.duration ?? "")}</p>
                            <video
                                src={personalSampleVideoPresigned.downloadUrl}
                                poster={personalSampleVideoSelected?.thumbnail}
                                style={{height: '280px', width: '100%', objectFit: 'contain'}}
                                className="mt-2 rounded"
                                controls={true}
                            />
                        </div>
                    )}
                    {personalVideoPreviewUrl && personalVideo && (
                        <div className="mt-4">
                            <h5 className="font-semibold">Face Video</h5>
                            <p>File name: {personalVideo.name}</p>
                            {personalVideoDuration && <p>Duration: {personalVideoDuration}</p>}
                            <video
                                src={personalVideoPreviewUrl}
                                style={{height: '280px', width: '100%', objectFit: 'contain'}}
                                className="mt-2 rounded"
                                controls={true}
                            />
                        </div>
                    )}

                    {/* Display recorded video */}
                    {recordedVideo && (
                        <div className="mt-3">
                            <video
                                src={URL.createObjectURL(recordedVideo)}
                                controls
                                className="w-full mt-2"
                            />
                        </div>
                    )}
                    {showCamera && (
                        <div className="mt-6 p-6 bg-[#F0EBF8] rounded-xl">
                            <Cameras onVideoCapture={handleVideoCapture}/>
                        </div>
                    )}

                    {personalVideo && personalVideoDuration && (
                        (() => {
                            // Parse the duration string correctly
                            let durationInSeconds = 0;
                            if (personalVideoDuration.includes(':')) {
                                const [minutes, seconds] = personalVideoDuration.split(':').map(Number);
                                durationInSeconds = minutes * 60 + seconds;
                            } else {
                                durationInSeconds = parseFloat(personalVideoDuration);
                            }
                            return durationInSeconds < MIN_DURATION || durationInSeconds > MAX_DURATION;
                        })()
                    ) && (
                        <p className="text-red-500 mt-2">
                            The video should be between {MIN_DURATION} and {MAX_DURATION} seconds long
                        </p>
                    )}

                </div>

                {/* YouTube Video Input */}
                <div className="p-6 bg-[#F0EBF8] shadow-xl rounded-xl">
                    <div className="flex items-center gap-2 text-[#673AB7] bg-transparent ">
                        <h5 className="mx-auto text-lg font-semibold -mt-2 -mb-1">Reference Video</h5>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <InfoIcon className="w-5 h-5 hover:cursor-pointer hover:text-[#F4804D]"/>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p className="max-w-xs">Paste URL of a video you want to copy content from. Videos with variety of different scenes are recommended!</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    <Separator className="my-4 bg-gray-300 "/>
                    <p className="text-gray-600 mt-3 mb-3">
                        Paste the YouTube link of a video you want to take inspiration from. For best results follow our
                        {" "}
                        <Dialog>
                            <DialogTrigger className="text-[#E46F3E] hover:underline cursor-pointer font-semibold">
                                recommended video types
                            </DialogTrigger>
                            <DialogContent title="Recommended Video Types" className={"min-w-1/2"}>
                                <DialogTitle>Recommended Video Types</DialogTitle>
                                <RecommendedVideoTypes/>
                                <DialogFooter>
                                    <DialogClose asChild>
                                        <Button
                                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                            aria-label="Close"
                                        >
                                            Close
                                        </Button>
                                    </DialogClose>
                                </DialogFooter>
                            </DialogContent>
                        </Dialog>

                        {" "}guideline.
                    </p>
                    <Input
                        type="url"
                        placeholder="Drop a YouTube URL here"
                        value={youtubeURL}
                        startIcon={Link}
                        onChange={(e) => setYoutubeURL(e.target.value)}
                        onBlur={(e) => {
                            if (e.target.value && !validateYouTubeUrl(e.target.value)) {
                                setError(true);
                                setErrorMessage('Please enter a valid YouTube URL.');
                                e.target.classList.add('border-red-500');
                            } else {
                                e.target.classList.remove('border-red-500');
                            }
                        }}
                        className="bg-white py-5 mt-1 border-gray-400 rounded-sm"
                        disabled={isDisabled()}
                    />

                    {/*Empty Div for padding */}
                    <div className={"pt-3"}/>

                    <SampleVideosModal videoType={"reference"} onSelectVideo={HandleReferenceVideoSelect} disabled={isDisabled()}/>

                    {youtubeURL && !validateYouTubeUrl(youtubeURL) ? (
                        <div className="mt-4 text-red-500 p-4 rounded-lg">
                            YouTube URL is not valid. Please enter a valid YouTube URL.
                        </div>
                    ) : (youtubeURL && youTubeVideoInfo) && (
                        <div className="mt-4">
                            <h5 className="font-semibold">YouTube Video</h5>
                            <p>Title: {youTubeVideoInfo.title}</p>
                            <p>Duration: {youTubeVideoInfo.duration}</p>
                            <iframe
                                src={`https://www.youtube.com/embed/${extractYouTubeVideoId(youtubeURL)}`}
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowFullScreen
                                style={{height: '280px', width: '100%', objectFit: 'contain'}}
                                className="mt-2 rounded"
                            />
                            {youTubeVideoInfo.exceeds_limit && (
                                <p className="text-red-500 mt-2">
                                    The video should be between 5 and 20 minutes long
                                </p>
                            )}
                        </div>
                    )}

                </div>
            </div>
            {(error || jobStatusError) && (
                <div className="bg-red-100 text-red-500 p-4 mt-4 rounded-lg">
                    {errorMessage || jobStatusError?.message}
                </div>
            )}

            <div
                className="p-4 flex flex-col sm:flex-row bg-[#F0EBF8] items-center mx-auto max-w-7xl justify-center mt-5 gap-4 shadow-xl rounded-xl">

                <div className="flex items-center w-full sm:w-auto mb-3 sm:mb-0">
                    <ResponsiveFusedVideoLengthDropdown
                        setFuseVideoLength={setFuseVideoLength}
                        fuseVideoLength={fuseVideoLength}
                        disabled={isDisabled()}
                    />
                </div>

                <Button
                    variant="outline"
                    className="bg-[#673AB7] w-full rounded-full sm:w-auto px-4 py-4 sm:px-7 sm:py-5
                    bg-gradient-to-r from-purple-800 to-purple-600
                    text-center text-white text-base sm:text-lg hover:opacity-90 hover:cursor-pointer hover:border hover:text-white hover:bg-[#5C33A5] mb-3 sm:mb-0"
                    onClick={handleFuseButtonCLick}
                    disabled={error || isDisabled()}
                >
                    {isPending || isProcessingModalOpen || isProcessing ? (
                        <div className="flex items-center justify-center gap-2">
                            <ClipLoader size={18} color="#ffffff"/>
                            <span>Processing...</span>
                        </div>
                    ) : <span className={"flex items-center justify-center gap-2"}>

                        Fuse Videos
                        <SparklesIcon/>
                    </span>}
                </Button>
                <div className="flex items-center w-full sm:w-auto justify-between sm:justify-start gap-4 sm:gap-2">
                    <Language onLangChange={handleLanguageSelect} currentLang={selectedLanguage} disabled={isDisabled()}/>
                    <div className="px-3 text-[#673AB7] bg-transparent">
                        <Tooltip>
                            <TooltipTrigger>
                                <InfoIcon className="w-5 h-5 hover:cursor-pointer hover:text-[#F4804D]"/>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="max-w-xs">Please select the desired language for your fused (generated) video. Content
                                    from your reference video will be translated to the selected language.</p>
                            </TooltipContent>
                        </Tooltip>
                    </div>
                </div>
            </div>
            {
                isProcessingModalOpen && (
                    <div className="flex flex-col items-center justify-center mx-auto space-y-6 py-6">
                        <div ref={progressRef} className="w-full max-w-xs">
                            <Progress
                                value={jobStatusData?.status === JobStatus.COMPLETED ? 100 : progressPercentage}
                                className="h-2 [&>*]:bg-gradient-to-r [&>*]:from-purple-800 [&>*]:to-purple-600"
                            />
                        </div>
                        <div className="text-sm text-gray-500">
                            {processingMessages[currentMessageIndex]} {Math.round(progressPercentage)}%
                        </div>
                    </div>
                )
            }


            {/* Lip Sync Confirmation Modal */}
            <Dialog open={confirmLipSyncModalOpen} onOpenChange={setConfirmLipSyncModalOpen}>
                <DialogContent className="sm:max-w-[500px] p-6 bg-[#F0EBF8] shadow-xl">
                    <DialogTitle className="text-[#673AB7] text-xl flex items-center gap-2">
                        <AlertCircle className="h-6 w-6"/> Add Lip Sync
                    </DialogTitle>
                    <DialogDescription className="text-gray-700 mt-2">
                        Are you sure you want to add lip sync to this video?
                    </DialogDescription>

                    <div className="my-4 p-4 bg-white rounded-md border border-gray-200">
                        <p className="text-gray-600 text-sm mt-1">
                            Adding lip sync will make the video's mouth movements match the audio more accurately.
                            This process may take a few minutes to complete.
                        </p>
                        <p className="text-gray-500 text-xs mt-2 italic">
                            The lip-synced version will be available alongside your original video when processing is
                            complete.
                        </p>
                    </div>
                    <DialogFooter className="flex justify-end gap-3">
                        <DialogClose asChild>
                            <Button
                                variant="outline"
                                className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 shadow-md hover:bg-[#F0EBF8] hover:text-[#673AB7] hover:cursor-pointer"
                            >
                                Cancel
                            </Button>
                        </DialogClose>
                        <Button
                            className="bg-[#673AB7] text-white hover:bg-[#5B33A0] shadow-md hover:cursor-pointer"
                            onClick={handleAddLipSync}
                            disabled={isAddingLipsync}
                        >
                            {isAddingLipsync ? (
                                <>
                                    <Loader className="h-4 w-4 animate-spin mr-2"/>
                                    Processing
                                </>
                            ) : (
                                <>Add Lip Sync</>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Lip Sync Confirmation Modal */}
            <Dialog open={confirmLipSyncModalOpen} onOpenChange={setConfirmLipSyncModalOpen}>
                <DialogContent className="sm:max-w-[500px] p-6 bg-[#F0EBF8] shadow-xl">
                    <DialogTitle className="text-[#673AB7] text-xl flex items-center gap-2">
                        <AlertCircle className="h-5 w-5"/> Lip Sync Cost
                    </DialogTitle>
                    <DialogDescription className="text-gray-700 -mt-2">
                        Adding lip sync to your video will incur an additional cost.
                    </DialogDescription>

                    <div className="p-4 bg-white rounded-md border border-gray-200">
                        <p className="text-gray-800 font-medium">Cost Details:</p>
                        <p className="text-[#F4804E] font-semibold text-lg mt-1">
                            ${jobStatusData?.lipsync_cost ? jobStatusData.lipsync_cost.toFixed(2) : '0.00'}
                        </p>
                        <p className="text-gray-600 text-sm mt-1">
                            This amount will be deducted from your credits once you choose to proceed and 
                            within 10–12 minutes a new lip synced version of the video will be created.
                        </p>
                        <p className="text-gray-600 text-md font-semibold mt-3">
                            What does lip sync do? </p>
                        <p className="text-gray-600 text-sm mt-3">
                            Lip sync improves the speaker’s mouth movements to better match the script.
                        </p>
                        <p className="text-gray-600 text-md font-semibold mt-3">
                            Important:
                        </p>
                        <p className="text-gray-600 text-sm mt-3">
                            Before adding lip sync to this video, make sure your video is finalized and you do not want
                            to reprocess it.
                            If you reprocess the video after applying lip sync, it will be removed and you'll need to
                            pay again to reapply it.
                        </p>
                    </div>
                    <DialogFooter className="flex justify-end gap-3">
                        <DialogClose asChild>
                            <Button
                                variant="outline"
                                className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 shadow-md hover:bg-[#F0EBF8] hover:text-[#673AB7] hover:cursor-pointer"
                            >
                                Not Now
                            </Button>
                        </DialogClose>
                        <Button
                            className="bg-[#673AB7] text-white hover:bg-[#5B33A0] shadow-md hover:cursor-pointer"
                            onClick={handleAddLipSync}
                            disabled={isAddingLipsync}
                        >
                            {isAddingLipsync ? (
                                <>
                                    <Loader className="h-4 w-4 animate-spin mr-2"/>
                                    Processing
                                </>
                            ) : (
                                <>Add Lip Sync</>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Retry Video Confirmation Modal */}
            <Dialog open={confirmRetryModalOpen} onOpenChange={setConfirmRetryModalOpen}>
                <DialogContent className="sm:max-w-[500px] p-6 bg-[#F0EBF8] shadow-xl">
                    <DialogTitle className="text-[#673AB7] text-xl flex items-center gap-2">
                        <AlertCircle className="h-6 w-6"/> Re-Process Video
                    </DialogTitle>
                    <DialogDescription className="text-gray-700 -mt-1">
                        {jobStatusData && jobStatusData.retry_count >= maxRetryAttempts ?
                            "You have used all available retry attempts for this video." :
                            "Are you sure you want to reprocess this video?"}
                    </DialogDescription>
                    <DialogDescription className="text-gray-700 -mt-1">
                        Reprocessing is completely free and won’t consume any credits. 
                        However, there is a limit to how many times you can reprocess each video.
                    </DialogDescription>

                    <div className="p-4 bg-white rounded-md border border-gray-200">
                        <p className="text-gray-800 font-medium">Reprocess Count:</p>
                        <div className="flex items-center justify-between mt-1">
                            <p className="text-[#F4804E] font-semibold text-lg">
                                {jobStatusData?.retry_count || 0} of {maxRetryAttempts} used
                            </p>
                        </div>
                        <p className="text-gray-600 text-md font-semibold mt-1">
                            Important:
                        </p>
                        <p className="text-gray-600 text-sm">
                            Reprocessing will replace your current video, and any applied lip sync will be lost.
                            If you'd like to keep the current version, please download it before reprocessing.
                            Reprocessing can take about 10–15 minutes depending on video duration and language complexity.
                        </p>
                        <p className="text-gray-500 text-xs mt-2 italic">
                            {maxRetryAttempts === 1 ?
                                "Free plan allows 1 re-processing per video. Upgrade to a paid plan for up to 3 re-processing per video." :
                                "Your subscription plan allows up to 3 re-processing per video."}
                        </p>
                    </div>
                    <DialogFooter className="flex justify-end gap-3">
                        <DialogClose asChild>
                            <Button
                                variant="outline"
                                className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 shadow-md hover:bg-[#F0EBF8] hover:text-[#673AB7] hover:cursor-pointer"
                            >
                                Cancel
                            </Button>
                        </DialogClose>
                        <Button
                            className="bg-[#673AB7] text-white hover:bg-[#5B33A0] shadow-md hover:cursor-pointer"
                            onClick={handleRetryVideo}
                            disabled={Boolean(jobStatusData && jobStatusData.retry_count >= maxRetryAttempts)}
                        >
                            Retry
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Lip Sync Video Modal */}
            <Dialog open={isLipSyncModalOpen} onOpenChange={setIsLipSyncModalOpen}>
                <DialogContent className="sm:max-w-[800px] p-0 bg-[#F0EBF8] shadow-xl overflow-hidden">
                    <div className="pt-3 px-4 font-semibold text-[#673AB7] text-lg">Lip Synced Video</div>
                    <div className="w-full overflow-hidden">
                        {selectedLipSyncUrl && (
                            <video
                                controls
                                autoPlay
                                className="w-full h-auto max-h-[70vh]"
                                poster={selectedLipSyncThumbnail || ""}
                            >
                                <source src={selectedLipSyncUrl} type="video/mp4"/>
                                Your browser does not support the video tag.
                            </video>
                        )}
                    </div>
                    <div className="p-4 flex justify-end -mt-4">
                        <DropdownMenu>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" size="icon"
                                                    className="h-11 w-11 sm:h-10 sm:w-10 lg:h-11 lg:w-11 xl:h-12 xl:w-12 bg-[#673AB7] text-white border-[#673AB7] border-2 hover:cursor-pointer hover:bg-[#673AB9] hover:text-white shadow-md"
                                                    disabled={!selectedLipSyncUrl}>
                                                <Download className="h-5 w-5 lg:h-6 lg:w-6"/>
                                            </Button>
                                        </DropdownMenuTrigger>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>Download video</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                            <DropdownMenuContent
                                className={"border border-[#673AB7] bg-[#F0EBF8]"}
                                align="end">
                                <Separator className="my-1"/>
                                <DropdownMenuItem
                                    className="mx-auto text-right text-[#673AB7] border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                    onClick={() => handleDownloadWithResolution('720p', true)}>
                                    Download Lip Synced Video
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </DialogContent>
            </Dialog>

            {jobStatusData?.status === JobStatus.COMPLETED && (
                <div className="mt-4">
                    <div className="bg-[#d1e7dd] text-[#0f5132] mt-4 border border-[#badbcc]
                    p-4 flex flex-col sm:flex-row  items-center mx-auto max-w-7xl justify-center mt-5 gap-4 shadow-xl rounded-xl
                    ">
                        Videos fused successfully! The fused video is ready to play.
                    </div>
                    <Separator/>
                    {jobStatusData.downloadUrl && (
                        <>
                            <Card className="mx-auto max-w-4xl mt-10 bg-[#F0EBF8] shadow-xl">
                                <CardHeader>
                                    <CardTitle className="text-3xl font-bold text-[#F4804E] text-center -pb-2 -pt-4">Fused
                                        Video</CardTitle>
                                </CardHeader>
                                <CardContent className="p-0">
                                    <video
                                        ref={videoRef}
                                        src={jobStatusData.downloadUrl}
                                        controls
                                        className="w-full rounded-lg max-h-[70vh] object-contain"
                                    />
                                </CardContent>
                                <CardFooter
                                    className="flex flex-col sm:flex-row justify-between items-center w-full gap-3 -pb-7">
                                    <div className="w-full sm:w-auto text-center sm:text-left">
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    {jobStatusData.lipsync_s3_key && jobStatusData.lipsync_s3_key !== "PROCESSING" ? (
                                                        <Button
                                                            variant={"outline"}
                                                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base sm:text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer lg:text-base xl:px-4 w-full sm:w-auto"
                                                            onClick={() => openLipSyncModal(jobStatusData.lipsync_s3_key ?? "", jobStatusData.thumbnail ?? "")}
                                                        >
                                                            View Lip Synced Video
                                                        </Button>
                                                    ) : (
                                                        <Button
                                                            variant={"outline"}
                                                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base sm:text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer lg:text-base xl:px-4 w-full sm:w-auto"
                                                            onClick={openConfirmLipSyncModal}
                                                            disabled={isAddingLipsync || Boolean(jobStatusData.lipsync_s3_key === "PROCESSING")}
                                                        >
                                                            {isAddingLipsync || Boolean(jobStatusData.lipsync_s3_key === "PROCESSING") ? <>
                                                                    <Loader className="h-4 w-4 animate-spin -mr-1"/>
                                                                    Processing
                                                                </> :
                                                                <>
                                                                    <Plus className="h-4 w-4 -mr-1"/>Lip Sync
                                                                    ${jobStatusData.lipsync_cost ? jobStatusData.lipsync_cost.toFixed(2) : '0.00'}
                                                                </>}
                                                        </Button>
                                                    )}
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    {jobStatusData.lipsync_s3_key && jobStatusData.lipsync_s3_key !== "PROCESSING" ?
                                                        'View the lip synced version of this video' :
                                                        isAddingLipsync || Boolean(jobStatusData.lipsync_s3_key === "PROCESSING") ?
                                                            'Lip sync is being processed...' :
                                                            'Add lip sync to this video'}
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                    <div className="flex gap-1 justify-center sm:justify-end w-full sm:w-auto">
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button variant="outline" size="icon"
                                                            className={`h-8 w-8 lg:h-9 lg:w-9 xl:h-9 xl:w-9 border-2 ${jobStatusData?.retry_count >= maxRetryAttempts ? 'bg-transparent border-gray-300 cursor-not-allowed' : 'bg-transparent border-[#673AB7] hover:cursor-pointer hover:bg-[#F0EBF8]'}`}
                                                            onClick={openConfirmRetryModal}
                                                            disabled={Boolean(retryProcessingVideoIds.includes(processedVideoID) || (jobStatusData && jobStatusData.retry_count >= maxRetryAttempts))}>
                                                        {retryProcessingVideoIds.includes(processedVideoID) ? (
                                                            <Loader
                                                                className="h-4 w-4 text-[#673AB7] lg:h-5 lg:w-5 animate-spin"/>
                                                        ) : jobStatusData?.retry_count >= maxRetryAttempts ? (
                                                            <RefreshCw className="h-4 w-4 text-gray-400 lg:h-5 lg:w-5"/>
                                                        ) : (
                                                            <RefreshCw
                                                                className="h-4 w-4 text-[#673AB7] lg:h-5 lg:w-5"/>
                                                        )}
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    {jobStatusData?.retry_count >= maxRetryAttempts ?
                                                        'You have used all available retry attempts' :
                                                        retryProcessingVideoIds.includes(processedVideoID) ?
                                                            'Processing retry request...' :
                                                            'Retry video processing'}
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>

                                        <DropdownMenu>
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="outline" size="icon"
                                                                    className="h-8 w-8 lg:h-9 lg:w-9 xl:h-9 xl:w-9 bg-[#673AB7] text-white border-gray-300 hover:cursor-pointer hover:bg-[#673AB9] hover:text-white">
                                                                {processing1080pVideoIds.includes(processedVideoID) ? (
                                                                    <Loader className="h-4 w-4 lg:h-5 lg:w-5 animate-spin"/>
                                                                ) : (
                                                                    <Download className="h-4 w-4 lg:h-5 lg:w-5"/>
                                                                )}
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Download video</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                            <DropdownMenuContent
                                                className={"border border-[#673AB7] bg-[#F0EBF8]"}
                                                align="end">
                                                <DropdownMenuItem
                                                    className="text-[#673AB7] border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                                    onClick={() => handleDownloadWithResolution('720p')}>
                                                    Download 720p <p className="mx-auto text-xs">(Instant Download)</p>
                                                </DropdownMenuItem>
                                                <Separator className="my-1"/>
                                                <DropdownMenuItem
                                                    className="mx-auto text-right text-[#673AB7] border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                                    onClick={() => handleDownloadWithResolution('1080p')}>
                                                    {jobStatusData?.download_1080p_s3_key === "PROCESSING" || processing1080pVideoIds.includes(processedVideoID) ? (
                                                        <div className="flex items-center justify-center gap-2">
                                                            <Loader
                                                                className="h-4 w-4 animate-spin -mr-1 text-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"/>
                                                            Exporting in 1080p
                                                        </div>
                                                    ) : !jobStatusData?.download_1080p_s3_key || jobStatusData?.download_1080p_s3_key === "" ? (
                                                        <>
                                                            Export in 1080p <p className="text-xs">(~10 minutes)</p>
                                                        </>
                                                    ) : (
                                                        <>Download 1080p</>
                                                    )}
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </CardFooter>
                            </Card>
                        </>
                    )}
                </div>
            )}

            <NotEnoughCreditsModal
                isOpen={showNotEnoughCreditsDialog}
                onOpenChange={(open) => {
                    if (!open) {
                        resetNotEnoughCreditsDialog();
                    }
                }}
            />


            <div>
                <UserFuseVideos/>
                <div className="mt-20"/>
            </div>

        </div>
    );
}

