import {useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ooter, <PERSON>alogClose, DialogTitle} from './ui/dialog';
import {Button} from './ui/button';
import {useSampleVideos, VideoReturnDTO} from '@/utils/hooks/video/useSampleVideos.ts';
import {Separator} from "@/components/ui/separator.tsx";

interface Props {
    videoType: 'personal' | 'reference';
    onSelectVideo: (video: VideoReturnDTO) => void;
    disabled?: boolean;
}

const VideoThumbnail = ({s3Key, title}: { s3Key: string; title: string }) => {
    return <img src={s3Key} className="w-20 h-auto rounded" alt={title}/>;
};


export const formatDuration = (durationStr: string) => {
    // time can be seperated by . or :
    if (durationStr === "") return "0:00";

    let seconds: number;

    if (durationStr.includes(':')) {
        // Format is already MM:SS
        const [minutes, secs] = durationStr.split(':').map(Number);
        seconds = minutes * 60 + secs;
    } else {
        // Format is seconds (possibly with decimal)
        seconds = Number(durationStr.split('.')[0]);
    }

    // Calculate minutes and remaining seconds
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    // Format as MM:SS
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}


export default function SampleVideosModal({videoType, onSelectVideo, disabled = false}: Props) {
    const {data, isLoading, error} = useSampleVideos(videoType);
    const [isOpen, setIsOpen] = useState(false);

    const handleVideoSelect = (video: VideoReturnDTO) => {
        setIsOpen(false);
        onSelectVideo(video);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline"
                        className="border-[#673AB7] text-[#673AB7] bg-transparent  border-2 text-base shadow-md mr-2 hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                        disabled={disabled}>
                    Sample Videos
                </Button>
            </DialogTrigger>

            <DialogContent className={
                "bg-[#F0EBF8]"
            }>
                <DialogTitle className={"text-[#673AB7]"}>
                    {videoType === 'personal' ? 'Sample Face Videos' : 'Recommended Videos'}
                </DialogTitle>

                {isLoading && <p>Loading...</p>}
                {error && <p className="text-red-500">Error loading videos: {error.message}</p>}

                <div className="max-h-[400px] bg-[#f7f2fa] overflow-auto space-y-2 ">
                    {data && data.length ? (
                        data.map((video) => (
                            <>
                                <div
                                    key={video.video_id}
                                    className="flex gap-3 items-center cursor-pointer hover:bg-white p-2 rounded"
                                    onClick={() => handleVideoSelect(video)}
                                >
                                    {videoType === 'personal' ? (
                                        <VideoThumbnail s3Key={video.thumbnail} title={video.title}/>
                                    ) : (
                                        <img src={video.thumbnail} className="w-20 h-auto rounded" alt={video.title}/>
                                    )}
                                    <div>
                                        <p className="font-medium">{video.title.replace('.mp4', '')}</p>
                                        {/*convert duration to minutes and seconds  */}
                                        <small
                                            className="text-gray-500">Duration: {formatDuration(video.duration)}</small>
                                    </div>
                                </div>
                                {/*    if its not the last item, add a separator */}
                                {data.indexOf(video) !== data.length - 1 && (
                                    <>
                                        <Separator className="my-2  bg-[#F0EBF8] w-full "/>
                                    </>
                                )}
                            </>
                        ))
                    ) : (
                        !isLoading && <p>No videos found.</p>
                    )}
                </div>

                <DialogFooter>
                    <DialogClose asChild>
                        <Button
                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 px-5 text-base shadow-md mr-2 hover:bg-[#673AB7] hover:text-white hover:cursor-pointer">Close</Button>
                    </DialogClose>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
