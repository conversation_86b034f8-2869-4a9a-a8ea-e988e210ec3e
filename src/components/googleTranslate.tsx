import { useEffect, useState } from "react";

export default function GoogleTranslate() {
    const [isScriptLoaded, setIsScriptLoaded] = useState(false);

    const googleTranslateElementInit = () => {
        window.googleTranslateElementInit = function () {
            if (window.google && window.google.translate) {
                new window.google.translate.TranslateElement(
                    {
                        pageLanguage: 'en',
                    },
                    'google_translate_element'
                );
            }
        };
    };

    useEffect(() => {
        // Create and load the Google Translate script
        const script = document.createElement('script');
        script.setAttribute(
            "src",
            "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
        );
        script.onload = () => setIsScriptLoaded(true); // Set the state when script is loaded
        document.body.appendChild(script);
        window.googleTranslateElementInit = googleTranslateElementInit;
    }, []);

    return (
        <div className="google-translate-container w-full md:w-64">
            {isScriptLoaded && (
                <div id="google_translate_element" className="mobile-visible"></div>
            )}
        </div>
    );
}
