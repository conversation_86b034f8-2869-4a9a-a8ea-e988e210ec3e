import {motion} from "framer-motion";
import {ArrowRight, Play} from "lucide-react";
import {GradientButton} from "@/components/ui/gradient-button";
import VideoPlayer from "@/components/HomeComponents/VideoPlayer.tsx";
import {useNavigate} from "@tanstack/react-router";
import {useEffect, useState} from "react";

export default function HeroSection() {
    // State to track if screen is small
    const [isSmallScreen, setIsSmallScreen] = useState(false);

    // Effect to check screen size and update on resize
    useEffect(() => {
        const checkScreenSize = () => {
            setIsSmallScreen(window.innerWidth < 1140);
        };

        // Check on mount
        checkScreenSize();

        // Add resize listener
        window.addEventListener('resize', checkScreenSize);

        // Cleanup
        return () => window.removeEventListener('resize', checkScreenSize);
    }, []);

    // Animation variants
    const containerVariants = {
        hidden: {opacity: 0},
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2,
            },
        },
    };

    const itemVariants = {
        hidden: {opacity: 0, y: 20},
        visible: {
            opacity: 1,
            y: 0,
            transition: {duration: 0.5},
        },
    };

    const navigate = useNavigate();

    return (
        <section className="relative overflow-hidden">
            {/* Gradient Background */}
            <div
                className="absolute inset-0 bg-gradient-to-b md:bg-gradient-to-r from-purple-700 to-orange-400 z-0"></div>
            <div className="relative z-10 px-4 pt-20 pb-20 sm:px-6 lg:px-8 lg:pt-24 lg:pb-28 max-w-7xl mx-auto">
                <div className="flex flex-col lg:flex-row items-center justify-between lg:gap-20">
                    {/* Hero Text Content */}
                    <motion.div
                        className="w-full lg:w-1/2 -mt-10 text-center lg:text-left mb-12 lg:mb-0"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                    >
                        <motion.div
                            className="inline-block px-3 py-1  rounded-full bg-white/20 text-white text-xs font-semibold mb-4"
                            variants={itemVariants}
                        >
                            2025 #1 Video Generation Tool by SU
                        </motion.div>

                        <motion.h1
                            className="text-4xl sm:text-5xl font-bold text-white leading-tight"
                            variants={itemVariants}
                        >
                            Use Camera Once.
                            <br className="hidden md:block"/>
                            <span className="block mt-2">And Never Again.</span>
                            <span className="block mt-2">
                Generate Realistic Videos 20x Faster.
              </span>
                        </motion.h1>

                        <motion.p
                            className="mt-6 text-lg text-white/90 max-w-2xl mx-auto lg:mx-0"
                            variants={itemVariants}
                        >
                            Clones AI learns your face, voice, and style—then uses the videos
                            you share as inspiration to create original content in your voice
                            and tone. No more timelines, cameras, or edits. You provide the
                            reference, AI handles the rest.
                        </motion.p>

                        <motion.div
                            className="mt-8 flex flex-col sm:flex-row justify-center lg:justify-start gap-4"
                            variants={itemVariants}
                        >
                            <div className="flex flex-col items-center">
                                <GradientButton
                                    variant="secondary"
                                    className="flex items-center justify-center gap-2 rounded-[20px] px-6 py-3 w-full sm:w-auto hover:cursor-pointer"
                                    onClick={() => navigate({to: "/signup"})}
                                >
                                    <Play className="h-5 w-5"/>
                                    Get Started for Free
                                </GradientButton>
                                <span className="text-xs text-white/80 mt-2 text-center w-full">
                  No credit card required
                </span>
                            </div>

                            <div className="flex flex-col items-center">
                                <GradientButton
                                    variant="outline"
                                    className="flex items-center justify-center gap-2 rounded-[20px] px-6 py-3 w-full sm:w-auto hover:cursor-pointer"
                                    onClick={() => navigate({to: "/playground"})}
                                >
                                    See How It Works
                                    <ArrowRight className="h-5 w-5"/>
                                </GradientButton>
                                <span className="text-xs text-white/80 mt-2 text-center w-full">
                  No credit card or sign up required
                </span>
                            </div>
                        </motion.div>
                    </motion.div>

                    {/* Hero Video Preview */}
                    <motion.div
                        className="w-full lg:w-1/2"
                        initial={{opacity: 0, scale: 0.95}}
                        animate={{opacity: 1, scale: isSmallScreen ? 1 : 1.2}}
                        transition={{duration: 0.6, delay: 0.3}}
                    >
                        <VideoPlayer
                            thumbnailUrl=""
                            videoUrl="https://clones-public.s3.us-east-1.amazonaws.com/video_files/clones-demo.mp4"
                        />
                    </motion.div>
                </div>
            </div>

            {/* White line cover */}
            <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-white z-[5]"></div>

            {/* Curved shape divider */}
            <div className="absolute bottom-0 left-0 right-0 overflow-hidden z-10">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 1440 120"
                    className="w-full h-auto text-white fill-current"
                    style={{ display: 'block' }}
                >
                    <path
                        d="M0,96L80,85.3C160,75,320,53,480,53.3C640,53,800,75,960,80C1120,85,1280,75,1360,69.3L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"></path>
                </svg>
            </div>
        </section>
    );
}
