import {motion, useAnimation} from "framer-motion";
import {useEffect} from "react";
import {useInView} from "react-intersection-observer";
import {Check, ArrowRight} from "lucide-react";
import {GradientButton} from "@/components/ui/gradient-button";
import VideoPlayer from "@/components/HomeComponents/VideoPlayer.tsx";
import {useNavigate} from "@tanstack/react-router";

export default function VideoShowcase() {
    const controls = useAnimation();
    const [ref, inView] = useInView({
        threshold: 0.1,
        triggerOnce: true,
    });

    useEffect(() => {
        if (inView) {
            controls.start("visible");
        }
    }, [controls, inView]);

    const containerVariants = {
        hidden: {},
        visible: {
            transition: {
                staggerChildren: 0.2,
            },
        },
    };

    const itemVariants = {
        hidden: {opacity: 0, x: -50},
        visible: {
            opacity: 1,
            x: 0,
            transition: {duration: 0.5},
        },
    };

    const navigate = useNavigate();

    return (
        <section ref={ref} className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
            <div className="max-w-7xl mx-auto">
                <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                    {/* Text Content */}
                    <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate={controls}
                        className="lg:pr-8 mb-10 lg:mb-0"
                    >
                        <motion.h2
                            variants={itemVariants}
                            className="text-3xl font-bold text-[#F4804E] sm:text-4xl mb-6"
                        >
                            Create Realistic Videos — Ready to Share Today
                        </motion.h2>

                        <motion.p
                            variants={itemVariants}
                            className="text-lg text-gray-600 mb-8"
                        >
                            Clones AI learns your full range of styles, expressions, and
                            emotions — then intelligently picks the most relevant ones to
                            match the topic and tone of your reference video. The result?
                            High-quality, social-ready videos created 20x faster — in just
                            minutes.
                        </motion.p>

                        <motion.ul variants={containerVariants} className="space-y-4">
                            <motion.li variants={itemVariants} className="flex items-start">
                                <Check className="h-6 w-6 text-green-500 mr-3 flex-shrink-0"/>
                                <span className="text-gray-700">
                  Cinematic quality with professional color grading
                </span>
                            </motion.li>
                            <motion.li variants={itemVariants} className="flex items-start">
                                <Check className="h-6 w-6 text-green-500 mr-3 flex-shrink-0"/>
                                <span className="text-gray-700">
                  Custom moods from energetic to heartfelt
                </span>
                            </motion.li>
                            <motion.li variants={itemVariants} className="flex items-start">
                                <Check className="h-6 w-6 text-green-500 mr-3 flex-shrink-0"/>
                                <span className="text-gray-700">
                  Match your unique style, your voice and visual identity
                </span>
                            </motion.li>
                        </motion.ul>

                        <motion.div variants={itemVariants} className="mt-8 flex flex-col sm:flex-row gap-4">
                            <div className="flex flex-col items-center sm:items-start">
                                <GradientButton className="flex items-center gap-2 w-full sm:w-auto"
                                                onClick={() => navigate({to: "/signup"})}
                                >
                                    Get Started for Free
                                    <ArrowRight className="h-5 w-5"/>
                                </GradientButton>
                                <span
                                    className="text-xs text-gray-500 mt-2 text-center w-full">No credit card required</span>
                            </div>

                            <div className="flex flex-col items-center sm:items-start">
                                <GradientButton variant="secondary"
                                                className="flex items-center gap-2 w-full sm:w-auto border border-gray-300"
                                                onClick={() => navigate({to: "/pricing"})}
                                >
                                    View Pricing Plans
                                </GradientButton>
                                <span className="text-xs text-orange-500 font-medium mt-2 text-center w-full">Exclusive discount ends soon!</span>
                            </div>
                        </motion.div>
                    </motion.div>

                    {/* Video Preview */}
                    <motion.div
                        initial={{opacity: 0, scale: 0.9}}
                        animate={
                            inView ? {opacity: 1, scale: 1} : {opacity: 0, scale: 0.9}
                        }
                        transition={{duration: 0.6}}
                        className="relative rounded-xl overflow-hidden shadow-xl"
                    >
                        <VideoPlayer
                            thumbnailUrl=""
                            videoUrl="https://clones-public.s3.us-east-1.amazonaws.com/video_files/AI-Person-62-96-zouFjgkE_4Y-es_summary_final.mp4"
                        />
                    </motion.div>
                </div>
            </div>
        </section>
    );
}
