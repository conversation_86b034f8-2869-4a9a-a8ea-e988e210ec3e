import {motion} from "framer-motion";
import {GradientButton} from "@/components/ui/gradient-button";
import {useNavigate} from "@tanstack/react-router";

export default function PricingCTA() {

    const navigate = useNavigate();

    return (
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
            <div className="max-w-4xl mx-auto text-center">
                <motion.h2
                    initial={{opacity: 0, y: -20}}
                    animate={{opacity: 1, y: 0}}
                    transition={{duration: 0.5}}
                    className="text-3xl font-bold text-[#F4804E] sm:text-4xl mb-6"
                >
                    Ready to transform your video production?
                </motion.h2>

                <motion.p
                    initial={{opacity: 0, y: -20}}
                    animate={{opacity: 1, y: 0}}
                    transition={{duration: 0.5, delay: 0.2}}
                    className="text-xl text-gray-600 mb-10"
                >
                    Join thousands of creators, marketers, and businesses who are already using Clones to create
                    professional videos at scale.
                </motion.p>

                <motion.div
                    initial={{opacity: 0, y: 20}}
                    animate={{opacity: 1, y: 0}}
                    transition={{duration: 0.5, delay: 0.4}}
                    className="flex flex-col sm:flex-row justify-center gap-4"
                >
                    <div className="flex flex-col items-center">
                        <GradientButton className="text-base px-6 py-3 w-full sm:w-auto"
                                        onClick={() => navigate({to: "/signup"})}
                        >
                            Get Started For Free
                        </GradientButton>
                        <span className="text-xs text-gray-500 mt-2 text-center w-full">No credit card required</span>
                    </div>

                    <div className="flex flex-col items-center">
                        <GradientButton variant="secondary"
                                        className="text-base px-6 py-3 border border-gray-300 w-full sm:w-auto"
                                        onClick={() => navigate({to: "/pricing"})}
                        >
                            View Pricing Plans
                        </GradientButton>
                        <span className="text-xs text-orange-500 font-medium mt-2 text-center w-full">Exclusive discount ends soon!</span>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}
