import {X} from "lucide-react";

interface AnnouncementBarProps {
    onClose: () => void;
}

export default function AnnouncementBar({onClose}: AnnouncementBarProps) {
    return (
        <div className="bg-primary text-white py-2 px-4 flex justify-center items-center relative">
            <p className="text-sm">Join weekly webinars in the community</p>
            <a href="https://community.heygen.com/public/events"
               className="ml-2 text-sm font-medium hover:underline flex items-center">
                Register Now
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 ml-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                    />
                </svg>
            </a>
            <button
                className="absolute right-4 text-white"
                aria-label="Close"
                onClick={onClose}
            >
                <X className="h-5 w-5"/>
            </button>
        </div>
    );
}
