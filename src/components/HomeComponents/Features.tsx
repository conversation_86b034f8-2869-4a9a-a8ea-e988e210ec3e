import { features } from "@/lib/constants";
import { motion, useAnimation } from "framer-motion";
import { useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { 
  User, 
  Mic, 
  Palette, 
  Clock, 
  CreditCard, 
  Languages,
  Scissors
} from "lucide-react";

// Icon mapping - all with consistent purple color
const iconMap: Record<string, React.ReactNode> = {
  Eye: <User className="h-6 w-6 text-purple-800" />,
  Mic: <Mic className="h-6 w-6 text-purple-800" />,
  Palette: <Palette className="h-6 w-6 text-purple-800" />,
  Clock: <Clock className="h-6 w-6 text-purple-800" />,
  CreditCard: <CreditCard className="h-6 w-6 text-purple-800" />,
  Languages: <Languages className="h-6 w-6 text-purple-800" />,
  Scissors: <Scissors className="h-6 w-6 text-purple-800" />,
};

export default function Features() {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  useEffect(() => {
    if (inView) {
      controls.start("visible");
    }
  }, [controls, inView]);

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold text-[#F4804E] sm:text-4xl"
          >
            Create professional videos in minutes
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto"
          >
            From idea to video, faster than ever, with cutting-edge AI
            technology that handles the complex work for you.
          </motion.p>
        </div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={controls}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            >
              <div className="p-6">
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                  {iconMap[feature.icon]}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
