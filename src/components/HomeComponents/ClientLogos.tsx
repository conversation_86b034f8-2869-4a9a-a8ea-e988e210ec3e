import { clientLogos } from "@/lib/constants";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";

export default function ClientLogos() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );
    
    const element = document.getElementById('client-logos');
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 0.6,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <section id="client-logos" className="py-12 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-xl font-semibold text-center text-gray-500 mb-8">
          Trusted by innovative companies worldwide
        </h2>
        
        <motion.div 
          className="flex flex-wrap justify-center items-center gap-x-8 gap-y-6"
          variants={containerVariants}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
        >
          {clientLogos.map((logo, index) => (
            <motion.div 
              key={index} 
              className="flex items-center justify-center h-12"
              variants={itemVariants}
            >
              <div className="h-8 w-auto grayscale hover:grayscale-0 transition-all duration-300">
                {/* Display logos using placeholder elements */}
                <div className="h-8 w-24 bg-gray-200 rounded-sm flex items-center justify-center text-gray-500 text-xs">
                  {logo.name}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
