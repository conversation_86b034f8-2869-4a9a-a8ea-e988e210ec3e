import {useEffect, useRef} from "react";

interface VideoPlayerProps {
    thumbnailUrl: string;
    videoUrl?: string;
    className?: string;
}

export default function VideoPlayer({thumbnailUrl, videoUrl, className}: VideoPlayerProps) {
    const videoRef = useRef<HTMLVideoElement>(null);

    // Auto-play video when component mounts
    useEffect(() => {
        if (videoRef.current) {
            void videoRef.current.play();
        }
    }, []);

    return (
        <div className={`relative rounded-xl overflow-hidden shadow-2xl ${className}`}>
            {/* Video element with poster/thumbnail */}
            <video
                ref={videoRef}
                className="w-full h-auto"
                poster={thumbnailUrl}
                muted={true}
                autoPlay
                playsInline
                loop
                src={videoUrl}
            />
        </div>
    );
}
