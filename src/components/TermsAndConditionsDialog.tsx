import {
    <PERSON>alog,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON>Header,
    DialogTitle,
    DialogDescription,
    DialogFooter,
    DialogClose
} from "@/components/ui/dialog";
import {But<PERSON>} from "@/components/ui/button";
import {ScrollArea} from "@/components/ui/scroll-area";

interface TermsAndConditionsDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export function TermsAndConditionsDialog({open, onOpenChange}: TermsAndConditionsDialogProps) {
    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-[800px] max-h-[80vh] overflow-hidden">
                <DialogHeader>
                    <DialogTitle className="text-[#673AB7]">Terms and Conditions</DialogTitle>
                    <DialogDescription>
                        Please read our terms and conditions carefully before using our service.
                    </DialogDescription>
                </DialogHeader>

                <ScrollArea
                    className="h-[50vh] mt-4 pr-4 scrollbar-thin scrollbar-thumb-[#673AB7] scrollbar-thumb-rounded-full scrollbar-track-transparent">
                    <div className="space-y-4 text-sm">
                        <h2 className="text-lg font-semibold">1. Introduction</h2>
                        <p>
                            Welcome to Clones. These Terms and Conditions govern your use of our website and services.
                            By accessing or using our services, you agree to be bound by these Terms.
                        </p>

                        <h2 className="text-lg font-semibold">2. Definitions</h2>
                        <p>
                            "Service" refers to the website and AI video generation services provided by Clones.
                            "User" refers to any individual who accesses or uses our Service.
                            "Content" refers to any videos, images, text, or other material that is uploaded, generated,
                            or otherwise available through our Service.
                        </p>

                        <h2 className="text-lg font-semibold">3. Account Registration</h2>
                        <p>
                            To use certain features of our Service, you may be required to register for an account. You
                            agree to provide accurate, current, and complete information during the registration process
                            and to update such information to keep it accurate, current, and complete.
                        </p>

                        <h2 className="text-lg font-semibold">4. User Responsibilities</h2>
                        <p>
                            You are responsible for maintaining the confidentiality of your account credentials and for
                            all activities that occur under your account. You agree to notify us immediately of any
                            unauthorized use of your account.
                        </p>

                        <h2 className="text-lg font-semibold">5. Acceptable Use</h2>
                        <p>
                            You agree not to use our Service to:
                        </p>
                        <ul className="list-disc pl-6 space-y-2">
                            <li>Violate any applicable laws or regulations</li>
                            <li>Infringe upon the rights of others</li>
                            <li>Generate or distribute harmful, offensive, or inappropriate content</li>
                            <li>Impersonate any person or entity</li>
                            <li>Interfere with or disrupt the Service</li>
                        </ul>

                        <h2 className="text-lg font-semibold">6. Intellectual Property</h2>
                        <p>
                            All content, features, and functionality of our Service are owned by Clones and are
                            protected by copyright, trademark, and other intellectual property laws.
                        </p>

                        <h2 className="text-lg font-semibold">7. User Content</h2>
                        <p>
                            By uploading content to our Service, you grant us a worldwide, non-exclusive, royalty-free
                            license to use, reproduce, modify, adapt, publish, translate, and distribute such content in
                            connection with providing our Service.
                        </p>

                        <h2 className="text-lg font-semibold">8. Privacy</h2>
                        <p>
                            Your use of our Service is also governed by our Privacy Policy, which is incorporated by
                            reference into these Terms and Conditions.
                        </p>

                        <h2 className="text-lg font-semibold">9. Video Attribution Requirements</h2>
                        <div className="space-y-2 pl-1">
                            <h3 className="font-medium">9.1 Acknowledgment of Attribution Requirement</h3>
                            <p>
                                By using Clones to generate videos, you acknowledge and agree that:
                            </p>
                            <ul className="list-disc pl-6 space-y-1">
                                <li>You are required to include the reference video's URL in the description when
                                    uploading the generated video to any platform.
                                </li>
                                <li>You understand that Clones does not own or claim rights to the reference video
                                    content and only provides a tool for video fusion and face cloning.
                                </li>
                            </ul>

                            <h3 className="font-medium mt-3">9.2 User Responsibility</h3>
                            <p>
                                You confirm that:
                            </p>
                            <ul className="list-disc pl-6 space-y-1">
                                <li>You are solely responsible for ensuring proper attribution is included when sharing
                                    the fused (generated) video.
                                </li>
                                <li>You understand that failure to include the source URL may result in claims from
                                    content owners, and you accept full responsibility for any such disputes.
                                </li>
                                <li>Clones is not responsible and cannot be held accountable for how users handle
                                    attribution to reference videos after video generation.
                                </li>
                            </ul>

                            <h3 className="font-medium mt-3">9.3 Limitation of Liability</h3>
                            <p>
                                You acknowledge that:
                            </p>
                            <ul className="list-disc pl-6 space-y-1">
                                <li>Clones is a software service that processes videos based on user input and does not
                                    engage in distributing, promoting, or monetizing the content generated.
                                </li>
                                <li>Any legal issues arising from the use, sharing, or distribution of the fused
                                    (generated) video are entirely your responsibility.
                                </li>
                                <li>Clones cannot be held liable for any copyright claims, takedowns, or disputes
                                    related to the content you generate.
                                </li>
                            </ul>
                        </div>

                        <h2 className="text-lg font-semibold">10. Termination</h2>
                        <p>
                            We reserve the right to terminate or suspend your account and access to our Service at our
                            sole discretion, without notice, for conduct that we believe violates these Terms or is
                            harmful to other users, us, or third parties, or for any other reason.
                        </p>

                        <h2 className="text-lg font-semibold">11. Limitation of Liability</h2>
                        <p>
                            To the maximum extent permitted by law, Clones shall not be liable for any indirect,
                            incidental, special, consequential, or punitive damages, or any loss of profits or revenues,
                            whether incurred directly or indirectly, or any loss of data, use, goodwill, or other
                            intangible losses resulting from your use of our Service.
                        </p>

                        <h2 className="text-lg font-semibold">12. Changes to Terms</h2>
                        <p>
                            We reserve the right to modify these Terms at any time. We will provide notice of any
                            material changes by posting the new Terms on our website. Your continued use of our Service
                            after such modifications will constitute your acknowledgment of the modified Terms.
                        </p>

                        <h2 className="text-lg font-semibold">13. Governing Law</h2>
                        <p>
                            These Terms shall be governed by and construed in accordance with the laws of the
                            jurisdiction in which Clones is established, without regard to its conflict of law
                            provisions.
                        </p>

                        <h2 className="text-lg font-semibold">14. Contact Information</h2>
                        <p>
                            If you have any questions about these Terms, please contact us through our Contact Us page.
                        </p>
                    </div>
                </ScrollArea>

                <DialogFooter className="mt-4">
                    <DialogClose asChild>
                        <Button
                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                            variant="outline"
                        >
                            Close
                        </Button>
                    </DialogClose>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
