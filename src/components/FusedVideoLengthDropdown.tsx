import {useState, useEffect, useMemo} from "react"
import {ChevronDown, InfoIcon, <PERSON>tings, Clock, Zap} from "lucide-react"
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from "@/components/ui/dropdown-menu"
import {Button} from "@/components/ui/button"
import {Separator} from "@radix-ui/react-separator";
import {useUserWithSubscription} from "@/utils/hooks/user/useUser";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip";

interface VideoLengthDropdownProps {
    fuseVideoLength: number
    setFuseVideoLength: (length: number) => void
    disabled?: boolean
}

export default function ResponsiveFusedVideoLengthDropdown({
                                                               fuseVideoLength,
                                                               setFuseVideoLength,
                                                               disabled = false
                                                           }: VideoLengthDropdownProps) {
    const [isMobile, setIsMobile] = useState(false)
    const [selectedLength, setSelectedLength] = useState<number>(fuseVideoLength)
    const {data: userData} = useUserWithSubscription()

    // Check if user has a free plan
    const isFreePlan = useMemo(() => {
        const subscription = userData?.subscription;
        return !subscription || !subscription.status || subscription.status !== "active" || subscription.productName.includes("Free");
    }, [userData]);

    // Check if user has a creator subscription
    const isCreatorPlan = useMemo(() => {
        const subscription = userData?.subscription;
        return subscription && subscription.status === "active" && subscription.productName.includes("Creator");
    }, [userData]);

    useEffect(() => {
        const checkIfMobile = () => {
            setIsMobile(window.innerWidth < 768)
        }

        checkIfMobile()
        window.addEventListener("resize", checkIfMobile)

        return () => {
            window.removeEventListener("resize", checkIfMobile)
        }
    }, [])

    // Update selected length when prop changes
    useEffect(() => {
        setSelectedLength(fuseVideoLength)
    }, [fuseVideoLength])

    // If user has a free plan and selected length is not 50, reset to 50
    useEffect(() => {
        if (isFreePlan && selectedLength !== 50 && fuseVideoLength !== 50) {
            setSelectedLength(50);
            setFuseVideoLength(50);
        }
    }, [isFreePlan])

    // If user has a creator plan, set default length to 90
    useEffect(() => {
        if (isCreatorPlan && selectedLength !== 90 && fuseVideoLength !== 90) {
            setSelectedLength(90);
            setFuseVideoLength(90);
        }
    }, [isCreatorPlan])

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    className="w-full sm:w-auto bg-white border border-[#673AB7] px-3 sm:px-4 py-2 text-sm focus:outline-none hover:cursor-pointer hover:bg-background"
                    disabled={disabled}
                >
                    <span className="flex items-center gap-2 text-gray-600"><Settings
                        className="h-4 w-4"/>Settings</span>
                    <ChevronDown className="ml-2 h-4 w-4 text-gray-600"/>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
                className={`p-0 overflow-hidden border bg-[#F0EBF8] border-[#673AB7] ${isMobile ? "w-[280px]" : "min-w-[320px] md:min-w-[400px]"}`}
                align="start"
            >
                <div className="rounded-t-lg text-[#673AB7] p-3">
                    <div className="flex items-center gap-2 text-[#673AB7] font-medium">
                        Fused Video Length
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <InfoIcon className=" w-5 h-5 hover:cursor-pointer hover:text-[#F4804D]"/>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p className="max-w-xs">Choose the desired length for the fused (generated)
                                        video</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                    </div>
                </div>
                <Separator className=" w-full h-0.5 bg-[#673AB7]"/>

                <div className={`gap-2 p-3 bg-white ${isMobile ? "flex flex-col" : "flex flex-row"}`}>
                    <DropdownMenuItem
                        className={`rounded-lg border border-[#673AB7] py-2 px-3 text-sm transition-colors flex items-center justify-center whitespace-nowrap hover:text-white
                        ${selectedLength === 50 ? 'bg-purple-100 text-purple-900 font-medium' : 'text-[#673AB7] hover:bg-[#673AB7]'}`}
                        onClick={() => {
                            setSelectedLength(50);
                            setFuseVideoLength(50);
                        }}
                    >
                        50s - 70s
                    </DropdownMenuItem>

                    {isFreePlan ? (
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger className="w-full">
                                    <div
                                        className="w-full rounded-lg border border-gray-400 py-2 px-3 text-sm text-gray-400 flex items-center justify-center whitespace-nowrap relative cursor-not-allowed">
                                        90s - 2min
                                        <span
                                            className="absolute -top-1 -right-1 text-xs font-medium text-orange-500 flex items-center gap-1">
                                            <Zap className="h-3 w-3 text-gold-700 fill-[#FFD700]"/>
                                        </span>
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <div className="flex items-center gap-1">
                                        <Zap className="h-3 w-3 text-gold-700 fill-[#FFD700]"/>
                                        <span>Please upgrade to use this feature</span>
                                    </div>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    ) : (
                        <DropdownMenuItem
                            className={`rounded-lg border border-[#673AB7] py-2 px-3 text-sm transition-colors flex items-center justify-center whitespace-nowrap relative
                            ${selectedLength === 90 ? 'bg-purple-100 text-purple-900 font-medium' : 'text-[#673AB7] hover:bg-[#673AB7] hover:text-white'}`}
                            onClick={() => {
                                setSelectedLength(90);
                                setFuseVideoLength(90);
                            }}
                        >
                            90s - 2min
                        </DropdownMenuItem>
                    )}

                    {isFreePlan ? (
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger className="w-full">
                                    <div
                                        className="w-full rounded-lg border border-gray-400 py-2 px-3 text-sm text-gray-400 flex items-center justify-center whitespace-nowrap relative cursor-not-allowed">
                                        2min - 3min
                                        <span
                                            className="absolute -top-1 -right-1 text-xs font-medium text-orange-500 flex items-center gap-1">
                                            <Zap className="h-3 w-3 text-gold-700 fill-[#FFD700]"/>
                                        </span>
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <div className="flex items-center gap-1">
                                        <Zap className="h-3 w-3 text-gold-700 fill-[#FFD700]"/>
                                        <span>Please upgrade to use this feature</span>
                                    </div>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    ) : (
                        <DropdownMenuItem
                            className={`rounded-lg border border-[#673AB7] py-2 px-3 text-sm transition-colors flex items-center justify-center whitespace-nowrap relative
                            ${selectedLength === 120 ? 'bg-purple-100 text-purple-900 font-medium' : 'text-[#673AB7] hover:bg-[#673AB7] hover:text-white'}`}
                            onClick={() => {
                                setSelectedLength(120);
                                setFuseVideoLength(120);
                            }}
                        >
                            2min - 3min
                        </DropdownMenuItem>
                    )}
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger className="w-full">
                                <div
                                    className="w-full rounded-lg border border-gray-400 py-2 px-3 text-sm text-gray-400 flex items-center justify-center whitespace-nowrap relative cursor-not-allowed">
                                    5min - 6min
                                    <span
                                        className="absolute -top-2 -right-2 text-xs font-medium  flex items-center gap-1">
                                        <Clock className="h-4 w-4 fill-orange-500 text-white"/>
                                    </span>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent>
                                <div className="flex items-center gap-1">
                                    <Clock className="h-3 w-3 fill-orange-500 text-white"/>
                                    <span>Coming soon</span>
                                </div>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
