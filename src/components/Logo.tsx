import {Link, useLocation} from "@tanstack/react-router";
import {triggerEvent} from "@/components/PageTracking.tsx";
import {useState, useEffect} from "react";

export default function Logo() {
    const location = useLocation();
    const isPlaygroundPage = location.pathname === "/playground";
    const [isMobile, setIsMobile] = useState(false);

    // Effect to check screen size and update on resize
    useEffect(() => {
        const checkScreenSize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        // Check on mount
        checkScreenSize();

        // Add resize listener
        window.addEventListener('resize', checkScreenSize);

        // Cleanup
        return () => window.removeEventListener('resize', checkScreenSize);
    }, []);

    return (
        <div className="flex-shrink-0 flex items-center">
            <Link
                to="/"
                className="font-bold text-2xl flex items-center gap-2 md:gap-3"
                onClick={() => triggerEvent({eventName: 'click_logo', page_section: 'header'})}
            >
                <svg
                    width={isMobile ? "40" : "57"}
                    height={isMobile ? "30" : "37"}
                    viewBox="0 0 55 40"
                    fill="none"
                    className="transition-all duration-300"
                >
                    {/* Back circles (clone) */}
                    <circle cx="30" cy="20" r="20" fill="#F4804E" opacity="0.5"/>
                    <path d="M33 20L21 27V13L33 20Z" fill="white" opacity="0.5"/>
                    <circle cx="25" cy="20" r="20" fill="#F4804E" opacity="0.4"/>
                    <path d="M33 20L21 27V13L33 20Z" fill="white" opacity="0.4"/>

                    {/* Front circle (original) */}
                    <circle cx="35" cy="20" r="20" fill="#F4804E"/>
                    <path d="M43 20L31 27V13L43 20Z" fill="white"/>
                </svg>
                <div className="flex items-center">
                    <span
                        className="-ml-1 md:-ml-2 text-xl md:text-2xl bg-clip-text text-transparent bg-gradient-to-r from-purple-700 to-purple-900 transition-all duration-300">
                        Clones
                    </span>
                    {isPlaygroundPage && (
                        <span className="text-[#f4804e] font-bold ml-1 md:ml-2 text-sm md:text-base pt-1 transition-all duration-300">
                            Playground
                        </span>
                    )}
                </div>
            </Link>
        </div>
    );
}