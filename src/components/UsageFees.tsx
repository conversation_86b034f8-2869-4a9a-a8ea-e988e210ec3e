import {Tooltip, TooltipContent, TooltipTrigger} from "@/components/ui/tooltip";
import {InfoIcon} from "lucide-react";

export interface UsageFeeConfig {
    videoWithoutLipsync: string;
    videoWithLipsync: string;
}

export const usageFeeConfigs: Record<string, UsageFeeConfig> = {
    free: {
        videoWithoutLipsync: "$1.5/min",
        videoWithLipsync: "$1.7 - $3.5/min",
    },
    creator: {
        videoWithoutLipsync: "$1.0/min",
        videoWithLipsync: "$1.2 - $3.0/min",
    }
};

interface UsageFeesProps {
    planType: 'free' | 'creator';
}

export function UsageFees({planType}: UsageFeesProps) {
    const fees = usageFeeConfigs[planType];

    if (!fees) return null;

    return (
        <div className="mt-6 border-t border-gray-200 pt-4">
            <div className="flex items-center mb-3">
                <h4 className="text-sm font-semibold text-[#673AB7]">Usage Fees</h4>
                <Tooltip>
                    <TooltipTrigger>
                        <InfoIcon className="h-4 w-4 ml-1 text-[#673AB7]"/>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p className="max-w-xs">This cost is based on processed video minutes and will be deducted from your credits. 
                            Lip-sync cost depends on often the speaker appears in the generated video.</p>
                    </TooltipContent>
                </Tooltip>
            </div>
            <ul className="text-sm space-y-3">
                <li className="flex justify-between items-center">
                    <span className="text-primary font-light ">Video generation with lipsync:</span>
                    <span className="text-primary font-light ">{fees.videoWithLipsync}</span>
                </li>
                <li className="flex justify-between items-center">
                    <span className="text-primary font-light">Video generation without lipsync:</span>
                    <span className="text-primary font-light">{fees.videoWithoutLipsync}</span>
                </li>
            </ul>
        </div>
    );
}
