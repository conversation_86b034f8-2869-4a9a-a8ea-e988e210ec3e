export default function RecommendedVideoTypes() {
    return (
        <div
            className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-800 border-y border-y-gray-200 pt-2 pb-4 overflow-y-auto max-h-[85vh] md:max-h-[70vh] scrollbar-thin scrollbar-thumb-[#673AB7] scrollbar-thumb-rounded-full scrollbar-track-transparent">
            <div
                className="p-2 sm:p-0 md:overflow-y-auto md:max-h-[70vh] md:scrollbar-thin md:scrollbar-thumb-[#673AB7] md:scrollbar-thumb-rounded-full md:scrollbar-track-transparent">
                <p className="text-sm sm:text-base">For best results, use a video that meets the following criteria.</p>
                <div className="border border-gray-300 p-2 sm:p-4 pt-1 rounded-md mt-2 bg-[#F0EBF8]">
                    <p className="text-base sm:text-lg font-semibold text-[#673AB7]">Criteria</p>
                    <ol className="list-decimal pl-5 text-sm sm:text-base">
                        <li>Single speaker, and</li>
                        <li>Variety of scenes</li>
                    </ol>
                </div>
                <p className="pt-2 text-base font-semibold sm:text-lg my-2">Explanation</p>
                <p className="text-sm sm:text-base">
                    Use a video with one main speaker throughout, but include a variety of other scenes — like
                    demonstrations, animations, or explanations — instead of showing the speaker the entire time.
                </p>
                <div className="bg-[#F8D7DB] border border-[#F0EBF8] text-[#842129] p-2 sm:p-4 rounded-md mt-2">
                    <p className="font-semibold text-sm sm:text-base">Note:</p>
                    <ul className="list-disc pl-5 text-sm sm:text-base">
                        <li>Avoid videos with only music or no audio.</li>
                        <li>Avoid screen-sharing or game streaming videos.</li>
                    </ul>
                </div>
            </div>
            <div
                className="p-2 sm:p-0 md:overflow-y-auto md:max-h-[70vh] md:scrollbar-thin md:scrollbar-thumb-[#673AB7] md:scrollbar-thumb-rounded-full md:scrollbar-track-transparent">
                <p className="font-medium text-sm sm:text-base">
                    Some example categories of recommended videos:
                </p>
                <ul className="list-disc pl-5 mt-2 space-y-1 sm:space-y-0 text-sm sm:text-base">
                    <li>News and Commentary</li>
                    <li>Product Reviews and Unboxings</li>
                    <li>Lifestyle and Wellness</li>
                    <li>Vlogs</li>
                    <li>Educational and Documentary-Style Content</li>
                    <li>Cooking and Recipe Videos</li>
                    <li>Motivational and Inspirational Videos</li>
                    <li>Tech Reviews and Tutorials</li>
                    <li>
                        Challenges and Pranks <span className="text-[#673AB7] italic text-xs sm:text-sm">(if single speaker)</span>
                    </li>
                    <li>
                        Reaction Videos <span
                        className="text-[#673AB7] italic text-xs sm:text-sm">(if no facecam box)</span>
                    </li>
                    <li>
                        Educational Tutorials{" "}
                        <span
                            className="text-[#673AB7] italic text-xs sm:text-sm">(if no facecam box or screensharing)</span>
                    </li>
                    <li>
                        Top Lists and Compilation Videos <span
                        className="text-[#673AB7] italic text-xs sm:text-sm">(if words used)</span>
                    </li>
                    <li>
                        DIY and Crafting <span
                        className="text-[#673AB7] italic text-xs sm:text-sm">(if words used)</span>
                    </li>
                    <li>
                        Lifestyle and Wellness <span className="text-[#673AB7] italic text-xs sm:text-sm">(if words used)</span>
                    </li>
                </ul>
            </div>
        </div>
    );
}
