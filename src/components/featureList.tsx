import {Feature} from "@/utils/hooks/payments/interfaces.ts";

import {
    Video,
    Subtitles,
    FileText,
    Upload,
    TextQuote,
    Smile,
    Calendar,
    Layout,
    Image as ImageIcon,
    Users,
    Zap,
    Gauge,
    Languages,
    Check,
    X,
    RefreshCw,
    Wallet,
    Cpu,
    Speech,
    type LucideIcon
} from 'lucide-react';
import {Tooltip, TooltipContent, TooltipTrigger} from "@/components/ui/tooltip.tsx";

const Icons: Record<string, LucideIcon> = {
    Video,
    Subtitles,
    FileText,
    Upload,
    TextQuote,
    Smile,
    Calendar,
    Layout,
    Image: ImageIcon,
    Users,
    Zap,
    Gauge,
    Languages,
    Check,
    X,
    RefreshCw,
    Wallet,
    Cpu,
    Speech
};


interface FeatureListProps {
    features: Feature[];
    billingCycle: "monthly" | "yearly";
}

export function FeatureList({features, billingCycle}: FeatureListProps) {
    const formatDuration = (value: string, unit: string) => {
        // Convert yearly values to monthly when showing monthly view
        if (unit === "year" && billingCycle === "monthly") {
            const numericValue = parseInt(value);
            return `${Math.round(numericValue / 12)} hours/month`;
        }
        return `${value} ${unit}`;
    };

    return (
        <ul className="mt-6 space-y-4">
            {features.map((feature) => {
                // Parse the description to handle duration formatting
                let description = feature.description;
                if (description) {
                    const match = description.match(/(\d+)\s+(hours\/year)/);
                    if (match) {
                        description = formatDuration(match[1], "year");
                    }
                }

                const Icon = Icons[feature.icon_name];

                return (
                    <li
                        key={feature.id}
                        className={`flex items-start gap-3 ${
                            feature.highlight ? 'text-primary' : ''
                        }`}
                    >
                        {feature.tooltip ? (
                            <Tooltip>
                                <TooltipTrigger>
                                    <Icon
                                        className={`h-5 w-5 ${
                                            feature.highlight
                                                ? 'text-primary'
                                                : 'text-muted-foreground'
                                        } mt-0.5`}
                                    />
                                </TooltipTrigger>
                                <TooltipContent>{feature.tooltip}</TooltipContent>
                            </Tooltip>
                        ) : (
                            <Icon
                                className={`h-5 w-5 ${
                                    feature.highlight
                                        ? 'text-primary'
                                        : 'text-muted-foreground'
                                } mt-0.5`}
                            />
                        )}
                        <div>
                            <span className="font-light">{feature.name}</span>
                            {description && (
                                <span className="text-muted-foreground ml-1">
                                    {description}
                                </span>
                            )}
                        </div>
                    </li>
                );
            })}
        </ul>
    );
}