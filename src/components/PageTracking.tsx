import {useLocation} from '@tanstack/react-router';
import {useEffect} from 'react';
import ReactGTM from 'react-gtm-module';


export type PagePath = keyof typeof GTMPageMapping
export const GTMPageMapping: { [key: string]: string } = {
    '/': 'home',
    '/studio': 'studio',
    '/playground': 'playground',
    '/dashboard': 'dashboard',
    '/subscription': 'subscription',
    '/settings': 'settings',
    '/waiting-list': 'waiting-list',
    '/contact-us': 'contact-us',
    '/login': 'login',
    '/signup': 'signup',
    '/pricing': 'pricing',
};


const getPageType = (pathname: string): PagePath => {
    const pageType = pathname.replace(/^\//, '');
    return GTMPageMapping[pathname] || pageType;
};


export default function usePageTracking() {
    const location = useLocation();
    useEffect(() => {
        const pageType = getPageType(location.pathname);
        ReactGTM.dataLayer({
            dataLayer: {
                event: 'custom_page_view',
                page: location.pathname,
                page_type: pageType,
                website_section: "main"
            }
        });
    }, [location]);

    return null;
}

export interface TriggerEventParams {
    eventName: string;
    page_section: string;
    name ?: string | null;
    value?: string | null;
}


export const triggerEvent = ({eventName, page_section, name = null, value = null}: TriggerEventParams) => {
    const location = window.location.pathname;
    const pageType = getPageType(location);
    ReactGTM.dataLayer({
        dataLayer: {
            event: eventName,
            page: location,
            page_type: pageType,
            website_section: page_section,
            name: name,
            value: value,
        },
    });
};

