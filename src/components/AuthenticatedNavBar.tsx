import {useState, useEffect, useRef} from "react";
import {<PERSON><PERSON>} from "@/components/ui/button.tsx";
import {ChevronDown, Info, Menu, Wallet, Zap, LogOut, SquarePen} from "lucide-react";
import {Sheet, SheetContent, SheetTrigger} from "@/components/ui/sheet.tsx";
import {Link, Outlet, useNavigate} from "@tanstack/react-router";
import {DBUser, UserSubscription, useUserWithSubscription} from "@/utils/hooks/user/useUser.ts";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle
} from "@/components/ui/card.tsx";
import {Separator} from "@/components/ui/separator.tsx";
import Logo from "@/components/Logo.tsx";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu.tsx";
import {useAuth} from "@/auth/use-auth.ts";
import {<PERSON><PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip.tsx";
import {useCheckAndUpdateUserName} from "@/utils/hooks/user/useCheckAndUpdateUserName.ts";


interface UserProfileProps {
    user?: DBUser | null;
}

const UserProfile = ({user}: UserProfileProps) => {
    // Get the first letter of the user's name or use a default
    const initials = user?.name ? user.name.charAt(0).toUpperCase() : "U";
    const {logout} = useAuth();
    const navigate = useNavigate();

    return (
        <div className="flex items-center gap-2 px-4 py-2 outline-2 outline-[#673ab7] rounded-md">
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <div
                        className="flex items-center gap-2 w-full hover:bg-purple-100 hover:cursor-pointer p-1 rounded-md transition-colors duration-200">
                        <div
                            className="flex h-8 w-8 items-center justify-center rounded-full outline-1 bg-[#673ab7] text-white outline-purple-300 text-purple-700">
                            <span className="text-sm font-semibold">{initials}</span>
                        </div>
                        <div className="flex items-center gap-1">
                            <span className="text-md ml-1 font-medium text-[#673ab7]">{user?.name || "User"}</span>
                        </div>
                        <ChevronDown className="h-4 w-4 text-[#673ab7] ml-auto"/>
                    </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="hover:text-white">
                    <DropdownMenuItem
                        className="text-right text-[#673AB7] border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer transition-colors duration-200"
                        onClick={
                            () => {
                                void navigate({to: "/pricing"});
                            }
                        }>
                        <SquarePen className="h-4 w-4 mr-2 text-[#673AB7] hover:text-white"/>
                        Manage Plan
                    </DropdownMenuItem>
                    <div className="my-0.5 h-px"/>
                    <DropdownMenuItem
                        className="text-right text-[#673AB7] border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer transition-colors duration-200"
                        onClick={logout}>
                        <LogOut className="h-4 w-4 mr-2 text-[#673AB7] hover:text-white"/>
                        Logout
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
};

interface CreditsSectionProps {
    user?: DBUser | null;
    isMobile?: boolean;
}

const CreditsSection = ({user, isMobile = false}: CreditsSectionProps) => (
    <div className={isMobile ? "mt-4 px-4 md:hidden" : "hidden items-center gap-2 md:flex"}>
        {isMobile ? (
            <div className="flex flex-col gap-2 mb-2">
                <div className="rounded-md px-3 py-1">
                    <span className="flex text-md font-bold text-[#673ab7]">
                        <Wallet className="h-6 w-6 mr-1 "/>
                        ${user?.credits || 0}
                    </span>
                </div>
                <Link to="/pricing" search={{section: "credits"}}>
                    <Button
                        className="w-full text-center px-3 py-2 rounded-[20px] text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                        size="sm">
                        Buy More Credits
                    </Button>
                </Link>
            </div>
        ) : (
            <>
                <div className="rounded-md px-3 py-1">
                    <span className="flex text-md font-bold text-[#673ab7]">
                        <Wallet className="h-6 w-6 mr-1 "/>
                        ${user?.credits || 0}
                    </span>
                </div>
                <Link to="/pricing" search={{section: "credits"}}>
                    <Button
                        className="w-full text-center px-5 py-4 rounded-[20px] text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                    >Buy More Credits</Button>
                </Link>
            </>
        )}
    </div>
);

interface PlanCardProps {
    user?: DBUser | null;
    subscription?: UserSubscription | null;
}

const PlanCard = ({user, subscription}: PlanCardProps) => {
    // Format date for display
    const formatDate = (UNIX_timestamp: string) => {
        const number = Number(UNIX_timestamp)
        const date = new Date(number * 1000);
        return date.toLocaleDateString("en-US", {
            day: "numeric",
            month: "short",
            year: "numeric"
        });
    };

    const freePlan = subscription?.productName.includes("Free");

    const isPaid = subscription?.status === "active";
    const planName = isPaid ? subscription.productName : "Free Plan";
    const expiryDate = isPaid ? formatDate(subscription.current_period_end) : "";
    // now from the plan name remove Clones from the plan name
    const planNameWithoutClones = planName.replace("Clones", "");
    // now remove - from the plan name
    const planNameWithoutDashes = planNameWithoutClones.replace("-", " ");

    const noOfFreeCredits = subscription?.productName.includes("Free") ? 5 : subscription?.productName.includes("Yearly") ? 360 : 30;

    return (
        <Card className="mt-4 drop-shadow-xl">
            <CardHeader>
                <CardTitle className="text-sm flex -mt-2 items-center justify-between w-full text-[#E46F3E]">
                    <div>{planNameWithoutDashes + " Plan"}</div>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Info className="h-4 w-4 text-[#E46F3E]"/>
                            </TooltipTrigger>
                            <TooltipContent>
                                <p className="max-w-xs"> {subscription?.productName.includes("Free") ? "You will " : "On your renewal date, you will "}receive
                                    ${noOfFreeCredits} free
                                    credits{subscription?.productName.includes("Free") ? " every month " : " "}and any
                                    unused purchased credits will expire.</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </CardTitle>
                <Separator className="mt-2 bg-gray-300 "/>
                <CardDescription className="mt-1 text-[#673AB7]">
                    ${user?.credits || 0} credits remaining
                </CardDescription>
            </CardHeader>
            <CardContent className="-mt-3">
                {isPaid && (
                    <div className="">
                        <span className="text-xs text-gray-500">Plan renews on {expiryDate}</span>
                    </div>
                )}
            </CardContent>
            <CardFooter className={"-mt-2"}>
                <Link to="/pricing" search={freePlan ? {section: "price"} : {section: "credits"}} className="w-full">
                    <Button
                        className="w-full border-[#673AB7] text-[#673AB7] bg-transparent  border-2 text-base shadow-md mr-2 hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                        size="sm" variant="outline">
                        <span className="mr-1">{freePlan ? "Upgrade" : "Add Credits"}</span>
                        <Zap className="h-4 w-4"/>
                    </Button>
                </Link>
            </CardFooter>
        </Card>
    );
};

const Navigation = () => (
    <div className="mt-auto pt-10 pb-1 space-y-1">
        <Link
            to="/contact-us"
            className="flex items-center rounded-md px-4 py-2 font-medium text-[#673AB7] hover:cursor-pointer"
        >
            Contact Us
        </Link>
    </div>
);

export default function AuthenticatedNavBar() {
    const [sheetOpen, setSheetOpen] = useState(false);
    const sheetRef = useRef<HTMLDivElement | null>(null);

    // Use the hook to check and update user name if needed
    useCheckAndUpdateUserName();

    // Fetch user and subscription data
    const {data} = useUserWithSubscription();
    const userData = data?.user;
    const subscriptionData = data?.subscription;

    // Check if screen is large and close sheet if needed
    const handleScreenSize = () => {
        const isLargeScreen = window.matchMedia('(min-width: 768px)').matches;
        if (isLargeScreen && sheetOpen) {
            setSheetOpen(false);
        }
    };

    // Run on mount and when screen size changes
    useEffect(() => {
        // Initial check
        handleScreenSize();

        // Add event listener for resize
        window.addEventListener('resize', handleScreenSize);

        // Cleanup
        return () => {
            window.removeEventListener('resize', handleScreenSize);
        };
    }, [sheetOpen]);

    return (
        <div className="flex min-h-screen flex-col ">
            {/* Top Navbar */}
            <header
                className="sticky top-0 z-30 flex h-16  items-center justify-between border-b bg-[#F0EBF8] px-4 md:px-6 shadow-md">
                <div className="flex items-center gap-4">
                    <Logo/>
                </div>
                <div className="flex items-center gap-3 ">
                    {/* Credits section - visible only on desktop */}
                    <CreditsSection user={userData}/>

                    {/* Mobile menu button */}
                    <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
                        <SheetTrigger asChild>
                            <Button variant="ghost" size="icon" className="md:hidden">
                                <Menu className="h-5 w-5 text-[#673AB7]"/>
                                <span className="sr-only">Open menu</span>
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="left" className="w-64 p-0 bg-[#F0EBF8] " ref={sheetRef}>
                            <div className="pt-5 pl-4">
                                <Logo/>
                            </div>
                            <div className="flex flex-col gap-2 p-4 h-full">
                                <UserProfile user={userData}/>
                                <CreditsSection user={userData} isMobile={true}/>
                                <div className="mt-2">
                                    <PlanCard user={userData} subscription={subscriptionData}/>
                                </div>
                                <div className="flex flex-col flex-grow justify-end">
                                    <Navigation/>
                                </div>
                            </div>
                        </SheetContent>
                    </Sheet>
                </div>
            </header>

            <div className="flex flex-1">
                <aside
                    className="hidden w-64 flex-col border-r bg-[#F0EBF8] md:flex fixed h-[calc(100vh-4rem)] overflow-y-auto z-20">
                    <div className="flex flex-col h-full p-4">
                        <UserProfile user={userData}/>
                        <PlanCard user={userData} subscription={subscriptionData}/>
                        <Navigation/>
                    </div>
                </aside>
                <div className={"container mx-auto px-3 pt-4 md:ml-64"}>
                    <Outlet/>
                </div>
            </div>
        </div>
    )
}