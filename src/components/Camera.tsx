import {useRef, useState, useEffect} from "react";
import Webcam from "react-webcam";
import {But<PERSON>} from "@/components/ui/button.tsx";
import {Camera, CircleStop} from "lucide-react";
import {MAX_DURATION, MIN_DURATION} from "@/utils/constants.ts";

export interface CameraProps {
    onVideoCapture: (videoBlob: Blob) => void;
    maxRecordingDuration?: number;
}

export default function Cameras({onVideoCapture, maxRecordingDuration = MAX_DURATION}: CameraProps) {
    const webcamRef = useRef<Webcam>(null);
    const webcamContainerRef = useRef<HTMLDivElement>(null);
    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const [recording, setRecording] = useState(false);
    const [videoUrl, setVideoUrl] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [duration, setDuration] = useState(0);
    const [isProcessing, setIsProcessing] = useState(false);
    const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
    const recordedChunks = useRef<Blob[]>([]);
    const [isCameraActive, setIsCameraActive] = useState(true);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const durationRef = useRef(0);
    const [isSafari, setIsSafari] = useState(false);
    const [hasMediaRecorderSupport, setHasMediaRecorderSupport] = useState(true);

    useEffect(() => {
        // Detect Safari browser
        const isSafariBrowser = /^((?!chrome|android).)*safari/i.test(navigator.userAgent) ||
            /iPad|iPhone|iPod/.test(navigator.userAgent);
        setIsSafari(isSafariBrowser);

        // Check if MediaRecorder is supported
        setHasMediaRecorderSupport(typeof MediaRecorder !== 'undefined');

        return () => {
            if (intervalRef.current) clearInterval(intervalRef.current);
        };
    }, []);

    // Auto-scroll to webcam when component mounts
    useEffect(() => {
        if (webcamContainerRef.current && isCameraActive) {
            const elementPosition = webcamContainerRef.current.getBoundingClientRect().top + window.scrollY;
            window.scrollTo({
                top: elementPosition - 100,
                behavior: 'smooth'
            });
        }
    }, [isCameraActive]);

    const startRecording = () => {
        setError(null);
        setDuration(0);
        durationRef.current = 0;
        setVideoUrl(null);
        setRecordedBlob(null);

        if (!webcamRef.current?.stream) {
            setError("Camera access is required to record video");
            return;
        }

        // Check if MediaRecorder is supported
        if (!hasMediaRecorderSupport) {
            setError("Your browser doesn't support video recording. Please try using Safari on macOS or Chrome/Firefox on any device.");
            return;
        }

        recordedChunks.current = [];
        const stream = webcamRef.current.stream;

        // Determine supported MIME types
        let mimeType = "video/webm";
        let options = {};

        try {
            // For Safari/iOS, try to use mp4 format if available
            if (isSafari) {
                if (MediaRecorder.isTypeSupported("video/mp4")) {
                    mimeType = "video/mp4";
                    options = {mimeType: "video/mp4"};
                } else if (MediaRecorder.isTypeSupported("video/quicktime")) {
                    mimeType = "video/quicktime";
                    options = {mimeType: "video/quicktime"};
                } else {
                    // Fallback to default without specifying mimeType for Safari
                    mediaRecorderRef.current = new MediaRecorder(stream);
                    console.log("Using default MediaRecorder without mimeType for Safari");
                }
            } else {
                // For other browsers, check if webm is supported
                if (MediaRecorder.isTypeSupported("video/webm;codecs=vp9")) {
                    mimeType = "video/webm;codecs=vp9";
                    options = {mimeType: "video/webm;codecs=vp9"};
                } else if (MediaRecorder.isTypeSupported("video/webm")) {
                    mimeType = "video/webm";
                    options = {mimeType: "video/webm"};
                }
            }

            // Only create MediaRecorder with mimeType if it wasn't created in the Safari fallback case
            if (!mediaRecorderRef.current) {
                try {
                    mediaRecorderRef.current = new MediaRecorder(stream, options);
                    console.log(`Using MediaRecorder with mimeType: ${mimeType}`);
                } catch (e) {
                    console.error("MediaRecorder error:", e);
                    // Last resort fallback without specifying mimeType
                    mediaRecorderRef.current = new MediaRecorder(stream);
                    console.log("Fallback: Using MediaRecorder without mimeType specification");
                }
            }
        } catch (err) {
            console.error("Error setting up MediaRecorder:", err);
            setError("Your browser doesn't fully support video recording. Please try a different browser.");
            return;
        }

        mediaRecorderRef.current.ondataavailable = (event) => {
            if (event.data && event.data.size > 0) {
                recordedChunks.current.push(event.data);
                console.log(`Received data chunk of size: ${event.data.size} bytes`);
            }
        };

        mediaRecorderRef.current.onstop = () => {
            console.log("MediaRecorder onstop event fired");
            processRecordedChunks();
            if (intervalRef.current) clearInterval(intervalRef.current);
        };

        // Add error handler for MediaRecorder
        mediaRecorderRef.current.onerror = (event) => {
            console.error("MediaRecorder error:", event);
            setError("An error occurred during recording. Please try again or use a different browser.");
            setIsProcessing(false);
            setRecording(false);
            if (intervalRef.current) clearInterval(intervalRef.current);
        };

        // For Safari, we need to request data more frequently
        const timeslice = isSafari ? 1000 : undefined; // Request data every second on Safari

        try {
            mediaRecorderRef.current.start(timeslice);
            setRecording(true);
            console.log("MediaRecorder started successfully");
        } catch (err) {
            console.error("Error starting MediaRecorder:", err);
            setError("Failed to start recording. Please try a different browser.");
            return;
        }

        intervalRef.current = setInterval(() => {
            setDuration((prev) => {
                const newDuration = prev + 1;
                durationRef.current = newDuration;

                // Auto-stop recording when max duration is reached
                if (newDuration >= maxRecordingDuration) {
                    stopRecording();
                }
                return newDuration;
            });
        }, 1000);
    };

    const stopRecording = () => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
            setIsProcessing(true);
            try {
                mediaRecorderRef.current.stop();
                console.log("MediaRecorder stopped successfully");
            } catch (err) {
                console.error("Error stopping MediaRecorder:", err);
                // Even if there's an error, we should still try to process any chunks we have
                processRecordedChunks();
            }
            setRecording(false);
            if (intervalRef.current) clearInterval(intervalRef.current);
        }
    };

    // Helper function to process recorded chunks
    const processRecordedChunks = () => {
        if (recordedChunks.current.length === 0) {
            setError("No video data was captured. Please try again or use a different browser.");
            setIsProcessing(false);
            return;
        }

        // Use the appropriate MIME type based on browser
        const blobType = isSafari ? "video/mp4" : "video/webm";
        const blob = new Blob(recordedChunks.current, {type: blobType});
        const url = URL.createObjectURL(blob);
        setVideoUrl(url);
        setRecordedBlob(blob);

        if (durationRef.current >= MIN_DURATION) {
            setError(null);
        } else {
            setError(`Minimum recording duration is ${MIN_DURATION} seconds`);
        }

        setIsProcessing(false);
        setIsCameraActive(false);
    };

    const handleRerecord = () => {
        setVideoUrl(null);
        setError(null);
        setRecordedBlob(null);
        setIsCameraActive(true);
        setDuration(0);
        durationRef.current = 0;
    };

    return (
        <div>
            {isCameraActive && (
                <div ref={webcamContainerRef}>
                    <Webcam
                        ref={webcamRef}
                        audio={true}
                        muted
                        videoConstraints={{
                            facingMode: "user",
                            // Add specific settings for better iOS compatibility
                            width: {ideal: 1280},
                            height: {ideal: 720}
                        }}
                        audioConstraints={{
                            // Ensure audio is properly captured on Safari
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }}
                        // Force user media to reload on Safari/iOS
                        forceScreenshotSourceSize={isSafari}
                        // Add specific Safari settings
                        screenshotFormat={isSafari ? "image/jpeg" : "image/webp"}
                        // Set a higher quality for screenshots
                        screenshotQuality={1}
                        // Add error handler for getUserMedia
                        onUserMediaError={(err) => {
                            console.error("Error accessing camera/microphone:", err);
                            setError("Could not access camera or microphone. Please ensure you've granted the necessary permissions and try again.");
                        }}
                    />
                </div>
            )}
            {/* Show recording controls only when camera is active and no preview */}
            {isCameraActive && (

                <div className="flex flex-col gap-2 mt-3">
                    {recording && (
                        <div className="w-full mb-2">
                            <div className="flex justify-between text-xs mb-1">
                                <span>{duration}s</span>
                                <span>{maxRecordingDuration}s</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                    className={`h-2.5 rounded-full transition-all duration-300 ${maxRecordingDuration - duration <= 10 ? 'bg-orange-500' : 'bg-[#673AB7]'}`}
                                    style={{width: `${(duration / maxRecordingDuration) * 100}%`}}
                                ></div>
                            </div>
                            {duration >= MIN_DURATION && duration < maxRecordingDuration && (
                                <div className="text-xs mt-1 text-[#0f5132]">
                                    ✓ Minimum duration reached. You can stop recording now or continue up
                                    to {maxRecordingDuration}s.
                                </div>
                            )}
                            {maxRecordingDuration - duration <= 10 && maxRecordingDuration - duration > 0 && (
                                <div className="text-xs text-orange-500 mt-1 font-medium">
                                    ⚠️ Recording will automatically stop in {maxRecordingDuration - duration} seconds
                                </div>
                            )}
                        </div>
                    )}

                    <div className="flex flex-wrap gap-2">
                        <Button
                            onClick={startRecording}
                            disabled={recording}
                            variant={recording ? "destructive" : "default"}
                            className={"border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"}
                        >
                            {recording ? `Recording (${duration}s/${maxRecordingDuration}s)` : <> <Camera
                                className="h-5 w-5"/>Start
                                Recording </>}
                        </Button>
                        <Button
                            onClick={stopRecording}
                            disabled={!recording || duration < 2}
                            variant="destructive"
                            className={"hover:cursor-pointer"}
                        >
                            <CircleStop className="h-5 w-5"/> Stop
                            Recording {duration < MIN_DURATION ? `(need ${MIN_DURATION - duration}s more)` : ""}
                        </Button>
                    </div>
                </div>
            )}

            {isProcessing && (
                <div className="mt-4 text-center py-4">
                    <div className="animate-pulse">Processing video...</div>
                </div>
            )}

            {videoUrl && !isProcessing && (
                <div className="-mt-4">
                    <video src={videoUrl} controls className="w-full rounded-lg"/>

                    <div className="mt-3 space-x-2 flex items-center">
                        <Button onClick={handleRerecord} variant="outline"
                                className={"border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"}>
                            Re-record Video
                        </Button>
                        <Button
                            onClick={() => recordedBlob && onVideoCapture(recordedBlob)}
                            disabled={duration < MIN_DURATION}
                            variant="default"
                            className={"border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"}
                        >
                            Use This Video
                        </Button>

                        {duration >= MIN_DURATION ? (
                            <span className=" text-[#0f5132] ml-2">
                ✓ Valid duration ({duration}s)
              </span>
                        ) : (
                            <span className="text-red-500 ml-2">
                ✗ Too short ({duration}s)
              </span>
                        )}
                    </div>
                </div>
            )}

            {error && (
                <div className="text-red-500 mb-2 pt-2 text-base">
                    {error}
                </div>
            )}
        </div>
    );
}