import {RecentVideo, useGetUserVideos} from "@/utils/hooks/video/useGetUserVideos.ts";
import {But<PERSON>} from "@/components/ui/button.tsx";
import {Download, Eye, Plus, RefreshCw, AlertCircle, Loader} from "lucide-react";
import {Card, CardContent} from "@/components/ui/card.tsx";
import {useLipsync} from "@/utils/hooks/video/useLipsync.ts";
import {Tooltip, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip.tsx";
import {useState, useMemo} from "react";
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogDescription,
    DialogFooter,
    DialogClose
} from "@/components/ui/dialog.tsx";
import CustomLoader from "@/components/ui/CustomLoader.tsx";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu.tsx";
import {Separator} from "@radix-ui/react-separator";
import {useDownload1080p} from "@/utils/hooks/video/useDownload1080p.ts";
import {useRetryVideo} from "@/utils/hooks/video/useRetryVideo.ts";
import {useUserWithSubscription} from "@/utils/hooks/user/useUser.ts";

export default function UserFuseVideos() {
    const {data, error, isLoading} = useGetUserVideos();
    const {data: userData} = useUserWithSubscription();
    const [processingVideoIds, setProcessingVideoIds] = useState<string[]>([]);
    const [retryProcessingVideoIds, setRetryProcessingVideoIds] = useState<string[]>([]);
    const [processing1080pVideoIds, setProcessing1080pVideoIds] = useState<string[]>([]);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    // Determine max retry attempts based on subscription
    const maxRetryAttempts = useMemo(() => {
        const subscription = userData?.subscription;
        const isFreePlan = !subscription || !subscription.status || subscription.status !== "active" || subscription.productName.includes("Free");
        return isFreePlan ? 1 : 3;
    }, [userData]);
    const {mutate: addLipsync} = useLipsync({
        onSuccess: (data, variables) => {
            if (data.status === 'error') {
                setErrorMessage(data.message || 'Failed to add lip sync');
                setProcessingVideoIds(prev => prev.filter(id => id !== variables.video_id));
            }
        },
        onError: (error) => {
            setErrorMessage(error.message || 'An error occurred while adding lip sync');
            // Clear all processing states on error
            setProcessingVideoIds([]);
        }
    });
    const {mutate: retryVideo} = useRetryVideo({
        onSuccess: (data, variables) => {
            if (data.status === 'error') {
                setErrorMessage(data.message || 'Failed to reprocessing the video');
                setRetryProcessingVideoIds(prev => prev.filter(id => id !== variables.video_id));
            }
        },
        onError: (error) => {
            setErrorMessage(error.message || 'An error occurred while reprocessing the video');
            // Clear all processing states on error
            setRetryProcessingVideoIds([]);
        }
    });
    const {mutateAsync: download1080p} = useDownload1080p();
    const [isOpen, setIsOpen] = useState(false);
    const [selectedLipSyncVideo, setSelectedLipSyncVideo] = useState<RecentVideo | null>(null);
    const [confirmLipSyncModalOpen, setConfirmLipSyncModalOpen] = useState(false);
    const [selectedVideoForLipSync, setSelectedVideoForLipSync] = useState<RecentVideo | null>(null);
    const [confirmRetryModalOpen, setConfirmRetryModalOpen] = useState(false);
    const [selectedVideoForRetry, setSelectedVideoForRetry] = useState<RecentVideo | null>(null);

    if (isLoading) {
        //     add loader here
        return <CustomLoader color="#673AB7"/>;
    }

    if (error instanceof Error) {
        return (
            <div className="bg-red-100 text-red-500 p-4 mt-4 rounded-lg">
                There is error while loading your recent fusions
            </div>
        )
    }

    const handleAddLipSync = (videoId: string) => {
        setErrorMessage(null);
        setProcessingVideoIds(prev => [...prev, videoId]);
        addLipsync({video_id: videoId});
        setConfirmLipSyncModalOpen(false);
    };

    const openConfirmLipSyncModal = (video: RecentVideo) => {
        setSelectedVideoForLipSync(video);
        setConfirmLipSyncModalOpen(true);
    };

    const openLipSyncModal = (video: RecentVideo) => {
        setSelectedLipSyncVideo(video);
        setIsOpen(true);
    };

    const openConfirmRetryModal = (video: RecentVideo) => {
        setSelectedVideoForRetry(video);
        setConfirmRetryModalOpen(true);
    };

    const handleRetryVideo = (videoId: string) => {
        setErrorMessage(null);
        setRetryProcessingVideoIds(prev => [...prev, videoId]);
        retryVideo({video_id: videoId});
        setConfirmRetryModalOpen(false);
    };
    const handleDownloadWithResolution = async (video: RecentVideo, resolution: '720p' | '1080p', lipsynced: boolean) => {
        let url = "";

        try {
            if (lipsynced && video.lipsync_s3_key) {
                url = video.lipsync_s3_key;
            } else {
                if (resolution === '720p') {
                    url = video.downloadUrl;
                }
                if (resolution === '1080p') {
                    if (video.download_1080p_s3_key === "PROCESSING" || processing1080pVideoIds.includes(video.video_id)) {
                        setErrorMessage('Video is currently being processed for 1080p');
                        return;
                    } else if (!video.download_1080p_s3_key || video.download_1080p_s3_key === "") {
                        // Set processing state immediately
                        setProcessing1080pVideoIds(prev => [...prev, video.video_id]);

                        await download1080p({video_id: video.video_id}).then((data) => {
                                if (data.status === 'error') {
                                    setErrorMessage(data.message || 'Failed to download 1080p');
                                    // Remove from processing state if there's an error
                                    setProcessing1080pVideoIds(prev => prev.filter(id => id !== video.video_id));
                                }
                            }
                        );

                        return;
                    } else {
                        url = video.download_1080p_s3_key;
                    }
                }
            }

            if (url) {
                const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
                const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

                if (isIOS || isSafari) {
                    // Attempt to open in a new tab
                    const newWindow = window.open(url, '_blank');

                    // Fallback if blocked
                    if (!newWindow || newWindow.closed) {
                        const confirmDownload = confirm(
                            'iOS may block automatic downloads. Press OK to open the video in a new tab. ' +
                            'Once opened, press and hold the video to save it.'
                        );
                        if (confirmDownload) {
                            window.open(url, '_blank');
                        }
                    }
                } else {
                    // Standard download for other browsers
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `fused-video-${resolution}${lipsynced ? '-lipsync' : ''}.mp4`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
            }
        } catch (error) {
            setErrorMessage('Failed to download video');
        }
    };

    return (
        <div className="container mx-auto">
            {errorMessage && (
                <div className="bg-red-100 text-red-500 p-4 mt-4 rounded-lg">
                    {errorMessage}
                </div>
            )}

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[800px] p-0 bg-[#F0EBF8] shadow-xl overflow-hidden">
                    <div className="pt-3 px-4 font-semibold text-[#673AB7] text-lg">Lip Synced Video</div>
                    <div className="w-full overflow-hidden">
                        {selectedLipSyncVideo?.lipsync_s3_key && (
                            <video
                                controls
                                autoPlay
                                className="w-full h-auto max-h-[70vh]"
                            >
                                <source src={selectedLipSyncVideo.lipsync_s3_key} type="video/mp4"/>
                                Your browser does not support the video tag.
                            </video>
                        )}
                    </div>
                    <div className="p-4 flex justify-end -mt-4">
                        <DropdownMenu>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" size="icon"
                                                    className="h-11 w-11 sm:h-10 sm:w-10 lg:h-11 lg:w-11 xl:h-12 xl:w-12 bg-[#673AB7] text-white border-[#673AB7] border-2 hover:cursor-pointer hover:bg-[#673AB9] hover:text-white shadow-md"
                                                    disabled={!selectedLipSyncVideo?.lipsync_s3_key}>
                                                <Download className="h-5 w-5 lg:h-6 lg:w-6"/>
                                            </Button>
                                        </DropdownMenuTrigger>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>Download video</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                            <DropdownMenuContent
                                className={"border border-[#673AB7] bg-[#F0EBF8]"}
                                align="end">
                                <Separator className="my-1"/>
                                <DropdownMenuItem
                                    className="mx-auto text-right text-[#673AB7] border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                    onClick={() => selectedLipSyncVideo && handleDownloadWithResolution(selectedLipSyncVideo, '720p', true)}>
                                    Download Lip Synced Video
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Lip Sync Confirmation Modal */}
            <Dialog open={confirmLipSyncModalOpen} onOpenChange={setConfirmLipSyncModalOpen}>
                <DialogContent className="sm:max-w-[500px] p-6 bg-[#F0EBF8] shadow-xl">
                    <DialogTitle className="text-[#673AB7] text-xl flex items-center gap-2">
                        <AlertCircle className="h-5 w-5"/> Lip Sync Cost
                    </DialogTitle>
                    <DialogDescription className="text-gray-700 -mt-2">
                        Adding lip sync to your video will incur an additional cost.
                    </DialogDescription>

                    <div className="p-4 bg-white rounded-md border border-gray-200">
                        <p className="text-gray-800 font-medium">Cost Details:</p>
                        <p className="text-[#F4804E] font-semibold text-lg mt-1">
                            ${selectedVideoForLipSync?.lipsync_cost ? selectedVideoForLipSync.lipsync_cost.toFixed(2) : '0.00'}
                        </p>
                        <p className="text-gray-600 text-sm mt-1">
                            This amount will be deducted from your credits once you choose to proceed and 
                            within 10–12 minutes a new lip synced version of the video will be created.
                        </p>
                        <p className="text-gray-600 text-md font-semibold mt-3">
                            What does lip sync do? </p>
                        <p className="text-gray-600 text-sm mt-3">
                            Lip sync improves the speaker’s mouth movements to better match the script.
                        </p>
                        <p className="text-gray-600 text-md font-semibold mt-3">
                            Important:
                        </p>
                        <p className="text-gray-600 text-sm mt-3">
                            Before adding lip sync to this video, make sure your video is finalized and you do not want
                            to reprocess it.
                            If you reprocess the video after applying lip sync, it will be removed and you'll need to
                            pay again to reapply it.
                        </p>
                    </div>
                    <DialogFooter className="flex justify-end gap-3">
                        <DialogClose asChild>
                            <Button
                                variant="outline"
                                className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 shadow-md hover:bg-[#F0EBF8] hover:text-[#673AB7] hover:cursor-pointer"
                            >
                                Not Now
                            </Button>
                        </DialogClose>
                        <Button
                            className="bg-[#673AB7] text-white hover:bg-[#5B33A0] shadow-md hover:cursor-pointer"
                            onClick={() => selectedVideoForLipSync && handleAddLipSync(selectedVideoForLipSync.video_id)}
                        >
                            Add Lip Sync
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Retry Video Confirmation Modal */}
            <Dialog open={confirmRetryModalOpen} onOpenChange={setConfirmRetryModalOpen}>
                <DialogContent className="sm:max-w-[500px] p-6 bg-[#F0EBF8] shadow-xl">
                    <DialogTitle className="text-[#673AB7] text-xl flex items-center gap-2">
                        <AlertCircle className="h-6 w-6"/> Re-Process Video
                    </DialogTitle>
                    <DialogDescription className="text-gray-700 -mt-1">
                        {selectedVideoForRetry && selectedVideoForRetry.retry_count >= maxRetryAttempts ?
                            "You have used all available reprocessing attempts for this video." :
                            "Are you sure you want to reprocess this video?"}
                    </DialogDescription>
                    <DialogDescription className="text-gray-700 -mt-1">
                        Reprocessing is completely free and won’t consume any credits. 
                        However, there is a limit to how many times you can reprocess each video.
                    </DialogDescription>

                    <div className="p-4 bg-white rounded-md border border-gray-200">
                        <p className="text-gray-800 font-medium">Reprocess Count:</p>
                        <div className="flex items-center justify-between mt-1">
                            <p className="text-[#F4804E] font-semibold text-lg">
                                {selectedVideoForRetry?.retry_count || 0} of {maxRetryAttempts} used
                            </p>
                        </div>
                        <p className="text-gray-600 text-md font-semibold mt-1">
                            Important:
                        </p>
                        <p className="text-gray-600 text-sm">
                            Reprocessing will replace your current video, and any applied lip sync will be lost.
                            If you'd like to keep the current version, please download it before reprocessing.
                            Reprocessing can take about 10–15 minutes depending on video duration and language complexity.
                        </p>
                        <p className="text-gray-500 text-xs mt-2 italic">
                            {maxRetryAttempts === 1 ?
                                "Free plan allows 1 re-processing per video. Upgrade to a paid plan for up to 3 re-processing per video." :
                                "Your subscription plan allows up to 3 re-processing per video."}
                        </p>
                    </div>
                    <DialogFooter className="flex justify-end gap-3">
                        <DialogClose asChild>
                            <Button
                                variant="outline"
                                className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 shadow-md hover:bg-[#F0EBF8] hover:text-[#673AB7] hover:cursor-pointer"
                            >
                                Cancel
                            </Button>
                        </DialogClose>
                        <Button
                            className="bg-[#673AB7] text-white hover:bg-[#5B33A0] shadow-md hover:cursor-pointer"
                            onClick={() => selectedVideoForRetry && handleRetryVideo(selectedVideoForRetry.video_id)}
                            disabled={Boolean(selectedVideoForRetry && selectedVideoForRetry.retry_count >= maxRetryAttempts)}
                        >
                            Re-Process
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <h2 className="mb-3 text-2xl font-bold text-[#F4804E] pt-10 text-center lg:text-left">My Recent
                Fusions</h2>
            <div
                className="grid gap-6 md:gap-7 lg:gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-3 max-w-[2000px] mx-auto border-t border-gray-200">
                {data?.map((video: RecentVideo) => (
                    <Card key={video.video_id}
                          className="transition-transform hover:shadow-md hover:scale-[1.02] mt-2 overflow-hidden shadow-xl p-0 bg-[#F0EBF8] ">
                        <div className="w-full overflow-hidden aspect-video">
                            {video.status === "PROCESSING" || video.status === "FAILED" ? (
                                <img
                                    src={video.thumbnail_key}
                                    alt="Video thumbnail"
                                    className="w-full h-full object-cover"
                                />
                            ) : (
                                <video controls
                                       poster={video.thumbnail_key}
                                       className="w-full h-full object-cover">
                                    <source src={video.downloadUrl}
                                            type="video/mp4"/>
                                    Your browser does not support the video tag.
                                </video>
                            )}
                        </div>
                        <CardContent className="pb-3 bg-[#F0EBF8] -pt-2 ">
                            <div className="flex flex-col sm:flex-row justify-between items-center w-full gap-3">
                                <div className="w-full sm:w-auto text-center sm:text-left">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                {video.lipsync_s3_key && video.lipsync_s3_key !== "PROCESSING" ? (
                                                    <>
                                                        <Button
                                                            variant={"outline"}
                                                            className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-sm sm:text-base shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer lg:text-base w-full sm:w-auto"
                                                            onClick={() => openLipSyncModal(video)}
                                                        ><Eye className="h-4 w-4 -mr-1"/>Lip Sync
                                                        </Button>

                                                    </>
                                                ) : (
                                                    <Button
                                                        variant={"outline"}
                                                        className="border-[#673AB7] text-[#673AB7] bg-transparent border-2 text-sm sm:text-sm shadow-md hover:bg-[#673AB7] hover:text-white hover:cursor-pointer lg:text-base  w-full sm:w-auto"
                                                        onClick={() => openConfirmLipSyncModal(video)}
                                                        disabled={processingVideoIds.includes(video.video_id) || Boolean(video.lipsync_s3_key === "PROCESSING") || video.status === "PROCESSING" || video.status === "FAILED"}
                                                    >
                                                        {processingVideoIds.includes(video.video_id) || Boolean(video.lipsync_s3_key === "PROCESSING") ?
                                                            <>
                                                                <Loader className="h-4 w-4 animate-spin -mr-1"/>
                                                                Processing
                                                            </> :
                                                            <>
                                                                <Plus className="h-4 w-4 -mr-1"/>Lip Sync
                                                            </>
                                                        }
                                                    </Button>
                                                )}
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                {video.lipsync_s3_key && video.lipsync_s3_key !== "PROCESSING" ?
                                                    'View the lip synced version of this video' :
                                                    processingVideoIds.includes(video.video_id) || Boolean(video.lipsync_s3_key === "PROCESSING") ?
                                                        'Lip sync is being processed...' :
                                                        'Add lip sync to this video'}
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                                <div className="flex gap-1 justify-center sm:justify-end w-full sm:w-auto">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button variant="outline" size="icon"
                                                        className={`h-11 w-11 sm:h-09 sm:w-09 lg:h-09 lg:w-09 xl:h-10 xl:w-10 border-2 ${video.retry_count >= maxRetryAttempts ? 'bg-transparent border-gray-300 cursor-not-allowed' : 'bg-transparent border-[#673AB7] hover:cursor-pointer hover:bg-[#F0EBF8]'}`}
                                                        onClick={(video.status === "FAILED" && video.retry_count === -1) ? () => {} : () => openConfirmRetryModal(video)}
                                                        disabled={Boolean(retryProcessingVideoIds.includes(video.video_id) || video.retry_count >= maxRetryAttempts || video.status === "PROCESSING")}>
                                                    {retryProcessingVideoIds.includes(video.video_id) ? (
                                                        <Loader
                                                            className="h-5 w-5 text-[#673AB7] lg:h-6 lg:w-6 animate-spin"/>
                                                    ) : video.retry_count >= maxRetryAttempts ? (
                                                        <RefreshCw className="h-5 w-5 text-gray-400 lg:h-6 lg:w-6"/>
                                                    ) : (
                                                        <RefreshCw className="h-5 w-5 text-[#673AB7] lg:h-6 lg:w-6"/>
                                                    )}
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>
                                                    {retryProcessingVideoIds.includes(video.video_id) ? 'Reprocessing...' :
                                                        video.status === "PROCESSING" ? 'Video is processing...' :
                                                            (video.status === "FAILED" && video.retry_count === -1) ? 'Oops! Video fusion failed. Please scroll to the top and try the same video combination again.':
                                                                video.retry_count >= maxRetryAttempts ? 'No reprocessing attempts left' : 'Reprocess video'}
                                                </p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                    <DropdownMenu>
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="outline" size="icon"
                                                                className={`h-11 w-11 sm:h-09 sm:w-09 lg:h-09 lg:w-09 xl:h-10 xl:w-10 ${video.status === "PROCESSING" ? 'bg-gray-400 text-white border-gray-400 cursor-not-allowed' : 'bg-[#673AB7] text-white border-[#673AB7] border-2 hover:cursor-pointer hover:bg-[#673AB9] hover:text-white'} shadow-md`}
                                                                disabled={video.status === "PROCESSING" || video.status === "FAILED"}>
                                                            {processing1080pVideoIds.includes(video.video_id) ? (
                                                                <Loader className="h-5 w-5 lg:h-6 lg:w-6 animate-spin"/>
                                                            ) : (
                                                                <Download className="h-5 w-5 lg:h-6 lg:w-6"/>
                                                            )}
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>{video.status === "PROCESSING" ? "Video is processing..." :
                                                        processing1080pVideoIds.includes(video.video_id) ? "Exporting video..." :
                                                            "Download video"}
                                                    </p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                        <DropdownMenuContent
                                            className={"border border-[#673AB7] bg-[#F0EBF8]"}
                                            align="end">
                                            <DropdownMenuItem
                                                className="text-[#673AB7] font-medium border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                                onClick={() => handleDownloadWithResolution(video, '720p', false)}
                                                disabled={processing1080pVideoIds.includes(video.video_id) || video.status === "PROCESSING"}>
                                                Download 720p
                                            </DropdownMenuItem>
                                            <Separator className="my-1 "/>
                                            <DropdownMenuItem
                                                className="mx-auto text-right font-medium text-[#673AB7] border border-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"
                                                onClick={() => handleDownloadWithResolution(video, '1080p', false)}
                                                disabled={processing1080pVideoIds.includes(video.video_id) || video.status === "PROCESSING"}>
                                                {video.download_1080p_s3_key === "PROCESSING" || processing1080pVideoIds.includes(video.video_id) ? (
                                                    <div className="flex items-center justify-center gap-2">
                                                        <Loader
                                                            className="h-4 w-4 animate-spin -mr-1 text-[#673AB7] hover:bg-[#673AB7] hover:text-white hover:cursor-pointer"/>
                                                        Exporting in 1080p
                                                    </div>
                                                ) : video.download_1080p_s3_key && video.download_1080p_s3_key !== "" ? (
                                                    <>
                                                        Download 1080p
                                                    </>
                                                ) : (
                                                    <>
                                                        Export in 1080p <p className="text-xs">(~10 minutes)</p>
                                                    </>
                                                )}
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
                {/*    if no videos  */}
                {data?.length === 0 && (
                    <div className="items-center justify-center h-full w-full">
                        <h2 className=" text-md text-gray-500 muted-text w-full">No videos processed recently.</h2>
                    </div>
                )}
            </div>
        </div>
    );
}
