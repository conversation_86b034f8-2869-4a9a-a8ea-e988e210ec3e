import {
    Dialog, DialogClose,
    DialogContent,
    <PERSON>alogDescription,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
} from "@/components/ui/dialog.tsx";
import {AlertCircle, CreditCard, X} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";
import {useNavigate} from "@tanstack/react-router";

interface NotEnoughCreditsModalProps {
    isOpen: boolean;
    onOpenChange?: (open: boolean) => void;
}

export default function NotEnoughCreditsModal({isOpen, onOpenChange}: NotEnoughCreditsModalProps) {
    const navigate = useNavigate();

    const handlePurchaseCredits = () => {
        if (onOpenChange) onOpenChange(false);
        navigate({to: "/pricing", search: {section: "credits"}});
    };

    const handleViewPlans = () => {
        if (onOpenChange) onOpenChange(false);
        navigate({to: "/pricing"});
    };

    const handleO<PERSON>Change = (open: boolean) => {
        if (onOpenChange) {
            onOpenChange(open);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
            <DialogContent
                className="w-[90vw] max-w-[480px] p-0 border-purple-200 overflow-hidden shadow-xl ring-1 ring-black/5">
                <DialogClose asChild>
                    <button className="absolute right-4 top-4 z-10 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
                        <X className="h-5 w-5 text-white"/>
                        <span className="sr-only">Close</span>
                    </button>
                </DialogClose>

                <DialogHeader className="bg-purple-600 text-white rounded-t-lg px-6 pt-8 pb-6 shadow-inner">
                    <div className="flex items-center justify-center mb-4">
                        <AlertCircle className="h-12 w-12 drop-shadow-md"/>
                    </div>
                    <DialogTitle className="text-center text-2xl font-semibold drop-shadow-sm">No Credits
                        Left</DialogTitle>
                    <DialogDescription className="text-purple-100 text-center mt-2">
                        You've used all your available credits
                    </DialogDescription>
                </DialogHeader>

                <div className="px-6 py-6 text-center bg-white">
                    <div className="mb-6 text-gray-600 mx-auto">
                        <p className="mb-5">
                            Your account has run out of credits. Purchase more credits to continue using all features.
                        </p>
                        <div className="flex justify-center">
                            <div
                                className="inline-flex items-center justify-center rounded-full bg-purple-100 p-4 text-purple-600 shadow-md">
                                <CreditCard className="h-6 w-6"/>
                            </div>
                        </div>
                    </div>
                </div>

                <DialogFooter className="flex flex-col sm:flex-row gap-3 px-6 pb-6 bg-white">
                    <Button
                        className="w-full sm:flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 h-auto font-medium rounded-md shadow-md hover:shadow-lg transition-all"
                        onClick={handlePurchaseCredits}
                    >
                        Purchase Credits
                    </Button>
                    <Button
                        variant="outline"
                        className="w-full sm:flex-1 border-purple-200 text-purple-600 hover:bg-purple-50 hover:text-purple-700 py-2 h-auto font-medium rounded-md shadow-sm hover:shadow-md transition-all"
                        onClick={handleViewPlans}
                    >
                        View Plans
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}