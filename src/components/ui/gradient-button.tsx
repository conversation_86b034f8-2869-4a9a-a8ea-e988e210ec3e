import {ButtonHTMLAttributes, forwardRef} from "react";
import {cn} from "@/lib/utils";
import {Button} from "@/components/ui/button";

interface GradientButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
    variant?: "primary" | "secondary" | "outline";
    children: React.ReactNode;
}

const GradientButton = forwardRef<HTMLButtonElement, GradientButtonProps>(
    ({variant = "primary", className, children, ...props}, ref) => {
        return (
            <Button
                ref={ref}
                className={cn(
                    "flex items-center justify-center rounded-[20px]",
                    variant === "primary" && "bg-gradient-to-r from-purple-800 to-purple-600 text-white hover:opacity-90 hover:cursor-pointer",
                    variant === "secondary" && "bg-white text-purple-900 hover:bg-gray-100 hover:cursor-pointer",
                    variant === "outline" && "bg-transparent border border-white text-white hover:bg-white/10 hover:cursor-pointer",
                    className
                )}
                {...props}
            >
                {children}
            </Button>
        );
    }
);

GradientButton.displayName = "GradientButton";

export {GradientButton};
