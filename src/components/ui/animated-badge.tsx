import { motion } from "framer-motion";
import { Badge } from "./badge";

export function AnimatedBadge({ children, className }: { children: React.ReactNode; className?: string }) {
    return (
        <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
                type: "spring",
                stiffness: 260,
                damping: 20
            }}
        >
            <Badge className={className}>
                {children}
            </Badge>
        </motion.div>
    );
}
