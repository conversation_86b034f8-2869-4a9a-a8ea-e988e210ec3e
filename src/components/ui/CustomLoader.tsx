import {PropagateLoader, ClipLoader} from "react-spinners";

interface LoaderProps {
    color?: string;
    className?: string;
    size?: number;
    inline?: boolean;
}

const CustomLoader = ({color = "#673AB7", className = "", size = 15, inline = false}: LoaderProps) => {
    if (inline) {
        return <ClipLoader color={color} size={size} />;
    }

    return (
        <div className={`flex justify-center bg-[#F0EBF8] py-8 mt-6 rounded-md ${className}`}>
            <PropagateLoader color={color}/>
        </div>
    );
}

export default CustomLoader;