import {useEffect, useState} from "react";
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "./ui/select";
import {Languages} from "lucide-react";

export interface LanguageProps {
    onLangChange: (lang: string) => void;
    currentLang?: string;
    disabled?: boolean;
}

export default function Language({onLangChange, currentLang = "en", disabled = false}: LanguageProps) {
    // State to track the selected language
    const [selectedLang, setSelectedLang] = useState(currentLang);

    const handleChange = (newLang: string) => {
        setSelectedLang(newLang);
        // store language in local storage to persist it across sessions
        localStorage.setItem("language", newLang);
        onLangChange(newLang);
    };
    useEffect(() => {
        setSelectedLang(localStorage.getItem("language") || currentLang);
    }, []);

    return (
        <div className="relative w-full sm:w-auto">
            <span
                className="absolute -top-2 -right-4 bg-red-500 text-white text-xs px-1 font-bold rounded-sm z-10 pb-0.5 ">NEW</span>
            <Select value={selectedLang} onValueChange={handleChange}>
                <SelectTrigger
                    className="relative w-full sm:w-auto bg-white border border-[#673AB7] px-3 sm:px-4 py-2 text-sm focus:outline-none hover:cursor-pointer hover:bg-background "
                    disabled={disabled}>
                    <span className="flex items-center gap-2 text-black"><Languages className="h-4 w-4"/></span>
                    <SelectValue className={"text-black"} placeholder="Select language"/>
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="auto">Auto</SelectItem>
                    <SelectItem value="es">Español</SelectItem>
                    <SelectItem value="fr">Français</SelectItem>
                    <SelectItem value="ar">العربية</SelectItem>
                    <SelectItem value="hi">हिन्दी</SelectItem>
                    <SelectItem value="ur">اردو</SelectItem>
                    <SelectItem value="ja">日本語</SelectItem>
                    <SelectItem value="zh">中文</SelectItem>
                    <SelectItem value="de">Deutsch</SelectItem>
                    <SelectItem value="ko">한국어</SelectItem>
                    <SelectItem value="pt">Português</SelectItem>
                    <SelectItem value="it">Italiano</SelectItem>
                    <SelectItem value="id">Bahasa Indonesia</SelectItem>
                    <SelectItem value="nl">Nederlands</SelectItem>
                    <SelectItem value="tr">Türkçe</SelectItem>
                    <SelectItem value="fil">Filipino</SelectItem>
                    <SelectItem value="pl">Polski</SelectItem>
                    <SelectItem value="sv">Svenska</SelectItem>
                    <SelectItem value="bg">Български</SelectItem>
                    <SelectItem value="ro">Română</SelectItem>
                    <SelectItem value="cs">Čeština</SelectItem>
                    <SelectItem value="el">Ελληνικά</SelectItem>
                    <SelectItem value="fi">Suomi</SelectItem>
                    <SelectItem value="hr">Hrvatski</SelectItem>
                    <SelectItem value="ms">Bahasa Melayu</SelectItem>
                    <SelectItem value="sk">Slovenčina</SelectItem>
                    <SelectItem value="da">Dansk</SelectItem>
                    <SelectItem value="ta">தமிழ்</SelectItem>
                    <SelectItem value="uk">Українська</SelectItem>
                    <SelectItem value="ru">Русский</SelectItem>
                </SelectContent>
            </Select>
        </div>
    );
}
