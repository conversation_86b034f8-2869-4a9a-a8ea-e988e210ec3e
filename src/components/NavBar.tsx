import {Link, Outlet, useLocation} from "@tanstack/react-router";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu.tsx";
import {ChevronDown, Menu, X} from "lucide-react";
import {Button} from "@/components/ui/button.tsx";
import Footer from "@/components/Footer.tsx";
import {useAuth} from "@/auth/use-auth.ts";
import {useEffect, useState} from "react";
import {triggerEvent} from "@/components/PageTracking.tsx";
import Logo from "@/components/Logo.tsx";

export default function NavigationBar() {
    const {user, logout} = useAuth();
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const location = useLocation();
    const [currentPath, setCurrentPath] = useState("/");

    useEffect(() => {
        setIsAuthenticated(!!user && !user.isAnonymous);
    }, [user]);

    useEffect(() => {
        setCurrentPath(location.pathname);
    }, [location]);
    return (
        <>
            <div className="flex flex-col min-h-screen bg-white text-gray-900">
                <nav
                    className={"px-4 md:py-2 border-b border-gray-100 bg-white w-full fixed top-0 left-0 right-0 z-50 shadow-lg"}>
                    <div className="max-w-7xl mx-auto">
                        <div className="flex items-center justify-between h-13">
                            <Logo/>
                            <div className="flex md:hidden">
                                <button
                                    type="button"
                                    className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-primary hover:bg-gray-100"
                                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                >
                                    <span className="sr-only">Open main menu</span>
                                    {isMobileMenuOpen ? (
                                        <X className="block h-6 w-6"/>
                                    ) : (
                                        <Menu className="block h-6 w-6 text-[#673AB7]"/>
                                    )}
                                </button>
                            </div>
                            <div className="hidden md:flex md:items-center md:space-x-6">
                                {!isAuthenticated && currentPath !== "/" && (
                                    <div className="relative group">
                                        <Link
                                            to="/"
                                            className="flex items-center text-gray-700 hover:text-purple-800 px-3 py-2 rounded-md text-base font-sans"
                                            onClick={() => triggerEvent({
                                                eventName: 'click_home',
                                                page_section: 'header'
                                            })}
                                        >
                                            Home
                                        </Link>
                                    </div>
                                )}

                                {(currentPath !== "/playground" && currentPath !== "/studio") && (
                                    <div className="relative group">
                                        <Link
                                            to={isAuthenticated ? "/studio" : "/playground"}
                                            className="flex items-center text-gray-700 hover:text-purple-800 px-3 py-2 rounded-md text-base font-sans"
                                            onClick={() => triggerEvent({
                                                eventName: isAuthenticated ? 'click_studio' : 'click_playground',
                                                page_section: 'header'
                                            })}
                                        >
                                            {isAuthenticated ? "Studio" : "Playground"}
                                        </Link>
                                    </div>
                                )}

                                {currentPath !== "/pricing" && (
                                    <div className="relative group">
                                        <Link
                                            to="/pricing"
                                            className="flex items-center text-gray-700 hover:text-purple-800 px-3 py-2 rounded-md text-base font-sans"
                                            onClick={() => triggerEvent({
                                                eventName: 'click_pricing',
                                                page_section: 'header'
                                            })}
                                        >
                                            Pricing
                                        </Link>
                                    </div>
                                )}

                                {currentPath !== "/contact-us" && (
                                    <div className="relative group">
                                        <Link
                                            to="/contact-us"
                                            className="flex items-center text-gray-700 hover:text-purple-800 px-3 py-2 rounded-md text-base font-sans"
                                            onClick={() => triggerEvent({
                                                eventName: 'click_contact_us',
                                                page_section: 'header'
                                            })}
                                        >
                                            Contact Us
                                        </Link>
                                    </div>
                                )}
                            </div>
                            <div className="hidden md:flex md:items-center md:space-x-3">
                                {isAuthenticated ? (
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" className="rounded-full">
                                                {user?.displayName}
                                                <ChevronDown className="ml-1 h-4 w-4"/>
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent>
                                            <DropdownMenuItem onSelect={() => {
                                                triggerEvent({eventName: 'click_logout', page_section: 'header'})
                                                logout();
                                            }}>
                                                Logout
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                ) : (
                                    <>
                                        <Link
                                            to="/login"
                                            className="text-purple-800 hover:text-purple-900 px-3 py-2 rounded-md text-md font-medium"
                                            onClick={() => triggerEvent({
                                                eventName: 'click_login',
                                                page_section: 'header'
                                            })}
                                        >
                                            Login
                                        </Link>
                                        <Link
                                            to="/signup"
                                            className="bg-gradient-to-r from-purple-800 to-purple-600 text-white hover:opacity-90 px-5 py-2.5 rounded-[20px] text-sm font-semibold shadow-sm"
                                            onClick={() => triggerEvent({
                                                eventName: 'click_signup',
                                                page_section: 'header'
                                            })}
                                        >
                                            Sign up for free
                                        </Link>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Mobile menu */}
                {isMobileMenuOpen && (
                    <div
                        className="md:hidden bg-white border-t py-4 fixed top-11 left-0 right-0 z-40 max-h-[calc(100vh-4rem)] overflow-y-auto">
                        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 flex flex-col items-center">
                            {!isAuthenticated && currentPath !== "/" && (
                                <Link
                                    to="/"
                                    className="block w-full text-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-800 hover:bg-gray-50"
                                    onClick={() => {
                                        triggerEvent({eventName: 'click_home', page_section: 'mobile_menu'});
                                        setIsMobileMenuOpen(false);
                                    }}
                                >
                                    Home
                                </Link>
                            )}
                            {(currentPath !== "/playground" && currentPath !== "/studio") && (
                                <Link
                                    to={isAuthenticated ? "/studio" : "/playground"}
                                    className="block w-full text-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-800 hover:bg-gray-50"
                                    onClick={() => {
                                        triggerEvent({
                                            eventName: isAuthenticated ? 'click_studio' : 'click_playground',
                                            page_section: 'mobile_menu'
                                        });
                                        setIsMobileMenuOpen(false);
                                    }}
                                >
                                    {isAuthenticated ? "Studio" : "Playground"}
                                </Link>
                            )}
                            {currentPath !== "/pricing" && (
                                <Link
                                    to="/pricing"
                                    className="block w-full text-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-800 hover:bg-gray-50"
                                    onClick={() => {
                                        triggerEvent({
                                            eventName: 'click_pricing',
                                            page_section: 'mobile_menu'
                                        });
                                        setIsMobileMenuOpen(false);
                                    }}
                                >
                                    Pricing
                                </Link>
                            )}
                            {currentPath !== "/contact-us" && (
                                <Link
                                    to="/contact-us"
                                    className="block w-full text-center px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-purple-800 hover:bg-gray-50"
                                    onClick={() => {
                                        triggerEvent({
                                            eventName: 'click_contact_us',
                                            page_section: 'mobile_menu'
                                        });
                                        setIsMobileMenuOpen(false);
                                    }}
                                >
                                    Contact Us
                                </Link>
                            )}
                        </div>

                        <div className="pt-4 pb-3 border-t border-gray-200">
                            <div className="flex flex-col items-center space-y-3 px-4">
                                {isAuthenticated ? (
                                    <button
                                        className="w-full text-center px-3 py-2 rounded-md text-base font-medium text-purple-800 hover:bg-gray-50"
                                        onClick={() => {
                                            triggerEvent({eventName: 'click_logout', page_section: 'mobile_menu'});
                                            logout();
                                            setIsMobileMenuOpen(false);
                                        }}
                                    >
                                        Logout
                                    </button>
                                ) : (
                                    <>
                                        <Link
                                            to="/login"
                                            className="w-full text-center px-3 py-2 rounded-md text-base font-medium text-purple-800 hover:bg-gray-50"
                                            onClick={() => {
                                                triggerEvent({
                                                    eventName: 'click_login',
                                                    page_section: 'mobile_menu'
                                                });
                                                setIsMobileMenuOpen(false);
                                            }}
                                        >
                                            Login
                                        </Link>
                                        <Link
                                            to="/signup"
                                            className="w-full text-center px-3 py-2 rounded-[20px] text-base font-medium text-white bg-gradient-to-r from-purple-800 to-purple-600 hover:opacity-90 hover:cursor-pointer"
                                            onClick={() => {
                                                triggerEvent({
                                                    eventName: 'click_signup',
                                                    page_section: 'mobile_menu'
                                                });
                                                setIsMobileMenuOpen(false);
                                            }}
                                        >
                                            Sign up for free
                                        </Link>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                )}
                <main className="pt-5 md:pt-15 flex-grow ">
                    <Outlet/>
                </main>
                <Footer/>
            </div>
        </>
    );
}